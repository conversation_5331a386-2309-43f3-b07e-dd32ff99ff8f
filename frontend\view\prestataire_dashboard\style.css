/* Styles spécifiques au tableau de bord prestataire */

/* Toggle de disponibilité */
.availability-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-right: 1rem;
}

.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #27ae60;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

#availability-text {
    font-weight: 500;
    color: #333;
}

#availability-text.unavailable {
    color: #e74c3c;
}

/* Formulaire de devis */
.devis-form-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    max-width: 1200px;
}

.demande-selection {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.demandes-selection-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-height: 400px;
    overflow-y: auto;
}

.demande-selection-item {
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;
}

.demande-selection-item:hover {
    border-color: #3498db;
    background: #f8f9fa;
}

.demande-selection-item.selected {
    border-color: #3498db;
    background: #e3f2fd;
}

.demande-selection-item h4 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.demande-selection-item p {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.devis-form {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    max-height: 600px;
    overflow-y: auto;
}

/* Section matières premières */
.matiere-premiere-section {
    margin: 1.5rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.matiere-premiere-section h4 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.matiere-item {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr auto;
    gap: 1rem;
    align-items: end;
    margin-bottom: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 5px;
}

.matiere-item .form-group {
    margin-bottom: 0;
}

.remove-matiere {
    background: #e74c3c;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 0.5rem;
    cursor: pointer;
    height: 38px;
    width: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-matiere:hover {
    background: #c0392b;
}

/* Cards de demandes pour prestataires */
.demande-card-prestataire {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
    border-left: 4px solid #3498db;
}

.demande-card-prestataire:hover {
    transform: translateY(-3px);
}

.demande-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.demande-info h3 {
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.demande-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin: 1rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 5px;
}

.meta-item {
    display: flex;
    flex-direction: column;
}

.meta-label {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.meta-value {
    font-weight: 500;
    color: #2c3e50;
}

.demande-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-top: 1rem;
}

/* Évaluations */
.evaluation-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 1rem;
}

.evaluation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.evaluation-rating {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.rating-stars-display {
    display: flex;
    gap: 2px;
}

.rating-stars-display i {
    color: #f39c12;
    font-size: 1.2rem;
}

.rating-stars-display i.empty {
    color: #ddd;
}

.rating-number {
    font-weight: bold;
    color: #2c3e50;
}

.evaluation-client {
    color: #666;
    font-size: 0.9rem;
}

.evaluation-comment {
    color: #333;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.evaluation-date {
    color: #999;
    font-size: 0.8rem;
}

/* Statistiques du profil */
.profile-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 1.5rem 0;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.stat-label {
    color: #666;
    font-weight: 500;
}

.stat-value {
    color: #2c3e50;
    font-weight: bold;
    font-size: 1.1rem;
}

/* Badges de statut spécifiques */
.status-devis-propose {
    background: #fff3cd;
    color: #856404;
}

.status-accepte {
    background: #d1ecf1;
    color: #0c5460;
}

/* Boutons d'action spécialisés */
.btn-proposer-devis {
    background: #f39c12;
    color: white;
}

.btn-proposer-devis:hover {
    background: #e67e22;
}

.btn-update-status {
    background: #17a2b8;
    color: white;
}

.btn-update-status:hover {
    background: #138496;
}

/* Indicateurs de priorité */
.priority-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.priority-high {
    background: #e74c3c;
}

.priority-medium {
    background: #f39c12;
}

.priority-low {
    background: #27ae60;
}

/* Messages d'état */
.no-data {
    text-align: center;
    color: #666;
    font-style: italic;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Responsive pour prestataire */
@media (max-width: 768px) {
    .devis-form-container {
        grid-template-columns: 1fr;
    }
    
    .demande-meta {
        grid-template-columns: 1fr;
    }
    
    .matiere-item {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .availability-toggle {
        margin-right: 0;
        margin-bottom: 0.5rem;
    }
    
    .header-right {
        flex-direction: column;
        align-items: flex-end;
    }
    
    .profile-stats {
        grid-template-columns: 1fr;
    }
}

/* Animation pour les cartes */
@keyframes slideInUp {
    from {
        transform: translateY(30px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.demande-card-prestataire,
.evaluation-card {
    animation: slideInUp 0.3s ease;
}

/* Styles pour les notifications */
.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.nav-item {
    position: relative;
}

/* Amélioration des formulaires */
.form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #3498db;
}

.form-section h4 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

/* Styles pour les tooltips */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px 10px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 0.8rem;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Amélioration de l'accessibilité */
.btn:focus,
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: 2px solid #3498db;
    outline-offset: 2px;
}

/* Styles pour les états de chargement */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #666;
}

.loading i {
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
