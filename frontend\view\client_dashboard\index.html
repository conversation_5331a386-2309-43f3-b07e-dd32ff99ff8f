<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord Client - ServicePro</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-tools"></i>
                    <h2>ServicePro</h2>
                </div>
            </div>
            <nav class="sidebar-nav">
                <a href="#" onclick="showSection('dashboard')" class="nav-item active">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Tableau de bord</span>
                </a>
                <a href="#" onclick="showSection('demander-service')" class="nav-item">
                    <i class="fas fa-plus-circle"></i>
                    <span>Demander un service</span>
                </a>
                <a href="#" onclick="showSection('mes-demandes')" class="nav-item">
                    <i class="fas fa-list"></i>
                    <span>Mes demandes</span>
                </a>
                <a href="#" onclick="showSection('devis')" class="nav-item">
                    <i class="fas fa-file-invoice"></i>
                    <span>Devis</span>
                </a>
                <a href="#" onclick="showSection('factures')" class="nav-item">
                    <i class="fas fa-receipt"></i>
                    <span>Factures</span>
                </a>
                <a href="#" onclick="showSection('evaluations')" class="nav-item">
                    <i class="fas fa-star"></i>
                    <span>Évaluations</span>
                </a>
                <a href="#" onclick="showSection('plaintes')" class="nav-item">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Plaintes</span>
                </a>
                <a href="#" onclick="showSection('profil')" class="nav-item">
                    <i class="fas fa-user"></i>
                    <span>Mon profil</span>
                </a>
            </nav>
            <div class="sidebar-footer">
                <a href="../../index.html" class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
                <a href="#" onclick="logout()" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Déconnexion</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button class="sidebar-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 id="page-title">Tableau de bord</h1>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <span id="user-name">Chargement...</span>
                        <i class="fas fa-user-circle"></i>
                    </div>
                </div>
            </header>

            <!-- Dashboard Section -->
            <section id="dashboard" class="content-section active">
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-demandes">0</h3>
                            <p>Demandes totales</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="demandes-en-cours">0</h3>
                            <p>En cours</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="demandes-terminees">0</h3>
                            <p>Terminées</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-euro-sign"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="montant-total">0€</h3>
                            <p>Montant total</p>
                        </div>
                    </div>
                </div>

                <div class="dashboard-content">
                    <div class="recent-activity">
                        <h3>Activité récente</h3>
                        <div id="recent-activities" class="activity-list">
                            <!-- Les activités seront chargées ici -->
                        </div>
                    </div>
                    <div class="quick-actions">
                        <h3>Actions rapides</h3>
                        <div class="action-buttons">
                            <button onclick="showSection('demander-service')" class="action-btn">
                                <i class="fas fa-plus"></i>
                                Nouvelle demande
                            </button>
                            <button onclick="showSection('mes-demandes')" class="action-btn">
                                <i class="fas fa-list"></i>
                                Voir mes demandes
                            </button>
                            <button onclick="showSection('devis')" class="action-btn">
                                <i class="fas fa-file-invoice"></i>
                                Consulter devis
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Demander Service Section -->
            <section id="demander-service" class="content-section">
                <div class="section-header">
                    <h2>Demander un service</h2>
                    <p>Décrivez votre besoin et trouvez le bon prestataire</p>
                </div>
                
                <form id="service-request-form" class="service-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="service-category">Catégorie de service *</label>
                            <select id="service-category" required>
                                <option value="">Sélectionner une catégorie</option>
                                <option value="Plomberie">Plomberie</option>
                                <option value="Électricité">Électricité</option>
                                <option value="Ménage">Ménage</option>
                                <option value="Réparation électronique">Réparation électronique</option>
                                <option value="Cuisine">Cuisine</option>
                                <option value="Coiffure">Coiffure</option>
                                <option value="Jardinage">Jardinage</option>
                                <option value="Peinture">Peinture</option>
                                <option value="Menuiserie">Menuiserie</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="service-budget">Budget estimé (€)</label>
                            <input type="number" id="service-budget" min="0" step="0.01">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="service-description">Description détaillée *</label>
                        <textarea id="service-description" rows="4" required 
                                placeholder="Décrivez précisément votre besoin, les travaux à effectuer, etc."></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="service-address">Adresse des travaux</label>
                        <input type="text" id="service-address" 
                               placeholder="Adresse où le service doit être effectué">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="service-date-debut">Date de début souhaitée</label>
                            <input type="date" id="service-date-debut">
                        </div>
                        <div class="form-group">
                            <label for="service-date-fin">Date de fin souhaitée</label>
                            <input type="date" id="service-date-fin">
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane"></i>
                        Soumettre la demande
                    </button>
                </form>
            </section>

            <!-- Mes Demandes Section -->
            <section id="mes-demandes" class="content-section">
                <div class="section-header">
                    <h2>Mes demandes de service</h2>
                    <div class="filter-buttons">
                        <button onclick="filterDemandes('toutes')" class="filter-btn active">Toutes</button>
                        <button onclick="filterDemandes('en_attente')" class="filter-btn">En attente</button>
                        <button onclick="filterDemandes('en_cours')" class="filter-btn">En cours</button>
                        <button onclick="filterDemandes('termine')" class="filter-btn">Terminées</button>
                    </div>
                </div>
                
                <div id="demandes-list" class="demandes-grid">
                    <!-- Les demandes seront chargées ici -->
                </div>
            </section>

            <!-- Devis Section -->
            <section id="devis" class="content-section">
                <div class="section-header">
                    <h2>Devis reçus</h2>
                    <p>Consultez et gérez vos devis</p>
                </div>
                
                <div id="devis-list" class="devis-grid">
                    <!-- Les devis seront chargés ici -->
                </div>
            </section>

            <!-- Factures Section -->
            <section id="factures" class="content-section">
                <div class="section-header">
                    <h2>Mes factures</h2>
                    <p>Consultez vos factures et paiements</p>
                </div>
                
                <div id="factures-list" class="factures-grid">
                    <!-- Les factures seront chargées ici -->
                </div>
            </section>

            <!-- Évaluations Section -->
            <section id="evaluations" class="content-section">
                <div class="section-header">
                    <h2>Évaluations</h2>
                    <p>Évaluez les prestataires après leurs interventions</p>
                </div>
                
                <div id="evaluations-list" class="evaluations-grid">
                    <!-- Les évaluations seront chargées ici -->
                </div>
            </section>

            <!-- Plaintes Section -->
            <section id="plaintes" class="content-section">
                <div class="section-header">
                    <h2>Mes plaintes</h2>
                    <button onclick="showComplaintModal()" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Nouvelle plainte
                    </button>
                </div>
                
                <div id="plaintes-list" class="plaintes-grid">
                    <!-- Les plaintes seront chargées ici -->
                </div>
            </section>

            <!-- Profil Section -->
            <section id="profil" class="content-section">
                <div class="section-header">
                    <h2>Mon profil</h2>
                    <p>Gérez vos informations personnelles</p>
                </div>
                
                <form id="profile-form" class="profile-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="profile-nom">Nom *</label>
                            <input type="text" id="profile-nom" required>
                        </div>
                        <div class="form-group">
                            <label for="profile-prenom">Prénom *</label>
                            <input type="text" id="profile-prenom" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="profile-email">Email</label>
                        <input type="email" id="profile-email" readonly>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="profile-telephone">Téléphone</label>
                            <input type="tel" id="profile-telephone">
                        </div>
                        <div class="form-group">
                            <label for="profile-sexe">Sexe</label>
                            <select id="profile-sexe">
                                <option value="Homme">Homme</option>
                                <option value="Femme">Femme</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="profile-adresse">Adresse</label>
                        <input type="text" id="profile-adresse">
                    </div>
                    
                    <div class="form-group">
                        <label for="profile-naissance">Date de naissance</label>
                        <input type="date" id="profile-naissance">
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Sauvegarder
                    </button>
                </form>
            </section>
        </main>
    </div>

    <!-- Modal pour nouvelle plainte -->
    <div id="complaintModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('complaintModal')">&times;</span>
            <h2>Nouvelle plainte</h2>
            <form id="complaint-form">
                <div class="form-group">
                    <label for="complaint-prestataire">Prestataire concerné</label>
                    <select id="complaint-prestataire" required>
                        <option value="">Sélectionner un prestataire</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="complaint-sujet">Sujet *</label>
                    <input type="text" id="complaint-sujet" required>
                </div>
                <div class="form-group">
                    <label for="complaint-description">Description *</label>
                    <textarea id="complaint-description" rows="4" required></textarea>
                </div>
                <div class="form-group">
                    <label for="complaint-contenu">Détails supplémentaires</label>
                    <textarea id="complaint-contenu" rows="3"></textarea>
                </div>
                <button type="submit" class="btn btn-primary">Soumettre la plainte</button>
            </form>
        </div>
    </div>

    <!-- Modal pour évaluation -->
    <div id="evaluationModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('evaluationModal')">&times;</span>
            <h2>Évaluer le prestataire</h2>
            <form id="evaluation-form">
                <input type="hidden" id="eval-demande-id">
                <input type="hidden" id="eval-prestataire-id">
                
                <div class="form-group">
                    <label>Note (sur 5) *</label>
                    <div class="rating-stars">
                        <i class="fas fa-star" data-rating="1"></i>
                        <i class="fas fa-star" data-rating="2"></i>
                        <i class="fas fa-star" data-rating="3"></i>
                        <i class="fas fa-star" data-rating="4"></i>
                        <i class="fas fa-star" data-rating="5"></i>
                    </div>
                    <input type="hidden" id="eval-note" required>
                </div>
                
                <div class="form-group">
                    <label for="eval-commentaire">Commentaire</label>
                    <textarea id="eval-commentaire" rows="4" 
                             placeholder="Partagez votre expérience avec ce prestataire"></textarea>
                </div>
                
                <button type="submit" class="btn btn-primary">Soumettre l'évaluation</button>
            </form>
        </div>
    </div>

    <script src="../../script.js"></script>
    <script src="script.js"></script>
</body>
</html>
