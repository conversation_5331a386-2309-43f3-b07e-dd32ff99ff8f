// Configuration de l'API
const API_BASE_URL = '../backend/server';

// Variables globales
let currentUser = null;

// Initialisation de l'application
document.addEventListener('DOMContentLoaded', function() {
    // Vérifier si l'utilisateur est connecté
    checkAuthStatus();
    
    // Gestionnaires d'événements pour les formulaires
    setupEventListeners();
});

// Configuration des gestionnaires d'événements
function setupEventListeners() {
    // Formulaire de connexion
    document.getElementById('loginForm').addEventListener('submit', handleLogin);
    
    // Formulaires d'inscription
    document.getElementById('registerClientForm').addEventListener('submit', handleClientRegister);
    document.getElementById('registerPrestataireForm').addEventListener('submit', handlePrestataireRegister);
    
    // Formulaire de contact
    document.querySelector('.contact-form').addEventListener('submit', handleContactForm);
}

// Gestion de la navigation
function showSection(sectionName) {
    // Masquer toutes les sections
    document.querySelectorAll('.section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Afficher la section demandée
    document.getElementById(sectionName).classList.add('active');
    
    // Mettre à jour la navigation
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
    });
    
    // Activer le lien correspondant
    const activeLink = document.querySelector(`[onclick="showSection('${sectionName}')"]`);
    if (activeLink) {
        activeLink.classList.add('active');
    }
}

// Gestion des modals
function showLoginModal() {
    document.getElementById('loginModal').style.display = 'block';
}

function showRegisterModal() {
    document.getElementById('registerModal').style.display = 'block';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// Gestion des onglets d'inscription
function showRegisterTab(type) {
    // Masquer tous les formulaires
    document.querySelectorAll('.register-form').forEach(form => {
        form.classList.remove('active');
    });
    
    // Afficher le formulaire correspondant
    if (type === 'client') {
        document.getElementById('registerClientForm').classList.add('active');
    } else {
        document.getElementById('registerPrestataireForm').classList.add('active');
    }
    
    // Mettre à jour les onglets
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    event.target.classList.add('active');
}

// Fermer les modals en cliquant à l'extérieur
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
}

// Gestion de la connexion
async function handleLogin(event) {
    event.preventDefault();
    
    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;
    const userType = document.getElementById('userType').value;
    
    if (!userType) {
        showMessage('Veuillez sélectionner un type d\'utilisateur', 'error');
        return;
    }
    
    try {
        let endpoint = '';
        switch(userType) {
            case 'client':
                endpoint = 'client.php';
                break;
            case 'prestataire':
                endpoint = 'prestataire.php';
                break;
            case 'admin':
            case 'service_client':
                endpoint = 'admin.php';
                break;
        }
        
        const response = await fetch(`${API_BASE_URL}/${endpoint}?action=login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ email, password })
        });
        
        const result = await response.json();
        
        if (result.success) {
            currentUser = result;
            localStorage.setItem('currentUser', JSON.stringify(result));
            
            showMessage('Connexion réussie !', 'success');
            closeModal('loginModal');
            
            // Rediriger vers le tableau de bord approprié
            redirectToDashboard(userType);
        } else {
            showMessage(result.message || 'Erreur de connexion', 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur de connexion au serveur', 'error');
    }
}

// Gestion de l'inscription client
async function handleClientRegister(event) {
    event.preventDefault();
    
    const formData = {
        nom: document.getElementById('clientNom').value,
        prenom: document.getElementById('clientPrenom').value,
        email: document.getElementById('clientEmail').value,
        password: document.getElementById('clientPassword').value,
        numeroTelephone: document.getElementById('clientTelephone').value,
        adresse: document.getElementById('clientAdresse').value,
        dateNaissance: document.getElementById('clientDateNaissance').value,
        sexe: document.getElementById('clientSexe').value
    };
    
    try {
        const response = await fetch(`${API_BASE_URL}/client.php?action=register`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('Inscription réussie ! Vous pouvez maintenant vous connecter.', 'success');
            closeModal('registerModal');
            document.getElementById('registerClientForm').reset();
        } else {
            showMessage(result.message || 'Erreur lors de l\'inscription', 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur de connexion au serveur', 'error');
    }
}

// Gestion de l'inscription prestataire
async function handlePrestataireRegister(event) {
    event.preventDefault();
    
    const formData = {
        nom: document.getElementById('prestataireNom').value,
        prenom: document.getElementById('prestatairePrenom').value,
        email: document.getElementById('prestataireEmail').value,
        numTel: document.getElementById('prestataireTelephone').value,
        nin: document.getElementById('prestataireNin').value,
        typeService: document.getElementById('prestataireService').value,
        adresse: document.getElementById('prestataireAdresse').value,
        dateNaissance: document.getElementById('prestataireNaissance').value
    };
    
    try {
        const response = await fetch(`${API_BASE_URL}/prestataire.php?action=demande_inscription`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('Demande d\'inscription soumise avec succès ! Vous recevrez une réponse sous 48h.', 'success');
            closeModal('registerModal');
            document.getElementById('registerPrestataireForm').reset();
        } else {
            showMessage(result.message || 'Erreur lors de la soumission', 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur de connexion au serveur', 'error');
    }
}

// Gestion du formulaire de contact
function handleContactForm(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    
    // Simulation d'envoi (à remplacer par un vrai endpoint)
    showMessage('Message envoyé avec succès ! Nous vous répondrons dans les plus brefs délais.', 'success');
    form.reset();
}

// Vérifier le statut d'authentification
function checkAuthStatus() {
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
        currentUser = JSON.parse(savedUser);
        updateUIForLoggedInUser();
    }
}

// Mettre à jour l'interface pour un utilisateur connecté
function updateUIForLoggedInUser() {
    if (currentUser) {
        const authButtons = document.querySelector('.auth-buttons');
        authButtons.innerHTML = `
            <span>Bonjour, ${currentUser.email}</span>
            <button onclick="logout()" class="btn btn-outline">Déconnexion</button>
            <button onclick="redirectToDashboard('${currentUser.typeCompte}')" class="btn btn-primary">Tableau de bord</button>
        `;
    }
}

// Déconnexion
function logout() {
    currentUser = null;
    localStorage.removeItem('currentUser');
    
    // Restaurer les boutons d'authentification
    const authButtons = document.querySelector('.auth-buttons');
    authButtons.innerHTML = `
        <button onclick="showLoginModal()" class="btn btn-outline">Connexion</button>
        <button onclick="showRegisterModal()" class="btn btn-primary">Inscription</button>
    `;
    
    showMessage('Déconnexion réussie', 'success');
    showSection('accueil');
}

// Redirection vers le tableau de bord
function redirectToDashboard(userType) {
    switch(userType) {
        case 'client':
            window.location.href = 'view/client_dashboard/index.html';
            break;
        case 'prestataire':
            window.location.href = 'view/prestataire_dashboard/index.html';
            break;
        case 'admin':
            window.location.href = 'view/admin_dashboard/index.html';
            break;
        case 'service_client':
            window.location.href = 'view/service_client_dashboard/index.html';
            break;
        default:
            showMessage('Type d\'utilisateur non reconnu', 'error');
    }
}

// Affichage des messages
function showMessage(message, type = 'info') {
    // Supprimer les anciens messages
    const existingMessage = document.querySelector('.message');
    if (existingMessage) {
        existingMessage.remove();
    }
    
    // Créer le nouveau message
    const messageDiv = document.createElement('div');
    messageDiv.className = `message message-${type}`;
    messageDiv.innerHTML = `
        <span>${message}</span>
        <button onclick="this.parentElement.remove()" class="message-close">&times;</button>
    `;
    
    // Ajouter les styles CSS si pas encore fait
    if (!document.querySelector('#message-styles')) {
        const styles = document.createElement('style');
        styles.id = 'message-styles';
        styles.textContent = `
            .message {
                position: fixed;
                top: 100px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 5px;
                color: white;
                z-index: 3000;
                display: flex;
                align-items: center;
                gap: 1rem;
                animation: slideInRight 0.3s ease;
                max-width: 400px;
            }
            .message-success { background: #27ae60; }
            .message-error { background: #e74c3c; }
            .message-info { background: #3498db; }
            .message-warning { background: #f39c12; }
            .message-close {
                background: none;
                border: none;
                color: white;
                font-size: 1.2rem;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            @keyframes slideInRight {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
        `;
        document.head.appendChild(styles);
    }
    
    // Ajouter le message au DOM
    document.body.appendChild(messageDiv);
    
    // Supprimer automatiquement après 5 secondes
    setTimeout(() => {
        if (messageDiv.parentElement) {
            messageDiv.remove();
        }
    }, 5000);
}

// Fonctions utilitaires
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('fr-FR', {
        style: 'currency',
        currency: 'EUR'
    }).format(amount);
}

// Validation des formulaires
function validateEmail(email) {
    const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return re.test(email);
}

function validatePhone(phone) {
    const re = /^[0-9\s\-\+\(\)]{10,}$/;
    return re.test(phone);
}

// Gestion des erreurs globales
window.addEventListener('error', function(event) {
    console.error('Erreur JavaScript:', event.error);
    showMessage('Une erreur inattendue s\'est produite', 'error');
});

// Gestion des erreurs de fetch
window.addEventListener('unhandledrejection', function(event) {
    console.error('Promesse rejetée:', event.reason);
    showMessage('Erreur de connexion au serveur', 'error');
});

// Export des fonctions pour utilisation dans d'autres fichiers
window.ServiceProApp = {
    showMessage,
    formatDate,
    formatCurrency,
    validateEmail,
    validatePhone,
    API_BASE_URL,
    getCurrentUser: () => currentUser
};
