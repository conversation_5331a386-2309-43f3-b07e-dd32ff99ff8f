<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, GET, PUT, DELETE, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Gestion des requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once 'db.php';

$database = new Database();
$db = $database->getConnection();

$auth = new Auth($db);
$clientManager = new ClientManager($db);
$demandeManager = new DemandeClientManager($db);

// Récupérer l'action depuis l'URL ou POST
$action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

switch($action) {
    case 'login':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            if (!isset($input['email']) || !isset($input['password'])) {
                jsonResponse(array("success" => false, "message" => "Email et mot de passe requis"));
            }
            
            $result = $auth->login($input['email'], $input['password']);
            
            if ($result['success'] && ($result['typeCompte'] == 'client' || $result['typeCompte'] == 'admin')) {
                // Récupérer les informations du client
                if ($result['typeCompte'] == 'client') {
                    $clientInfo = $clientManager->getByAccountId($result['idCompte']);
                    $result['clientInfo'] = $clientInfo;
                }
                jsonResponse($result);
            } else {
                jsonResponse(array("success" => false, "message" => "Accès non autorisé pour ce type de compte"));
            }
        }
        break;

    case 'register':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            $required_fields = ['email', 'password', 'nom', 'prenom'];
            foreach ($required_fields as $field) {
                if (!isset($input[$field]) || empty($input[$field])) {
                    jsonResponse(array("success" => false, "message" => "Le champ $field est requis"));
                }
            }
            
            // Créer le compte
            $accountResult = $auth->register($input['email'], $input['password'], 'client');
            
            if ($accountResult['success']) {
                // Créer le profil client
                $clientResult = $clientManager->create(
                    $input['nom'],
                    $input['prenom'],
                    $input['numeroTelephone'] ?? '',
                    $input['dateNaissance'] ?? null,
                    $input['adresse'] ?? '',
                    $input['sexe'] ?? 'Homme',
                    $accountResult['idCompte']
                );
                
                if ($clientResult['success']) {
                    jsonResponse(array(
                        "success" => true,
                        "message" => "Inscription réussie",
                        "idCompte" => $accountResult['idCompte'],
                        "idClient" => $clientResult['idClient']
                    ));
                } else {
                    jsonResponse($clientResult);
                }
            } else {
                jsonResponse($accountResult);
            }
        }
        break;

    case 'profile':
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            $idCompte = isset($_GET['idCompte']) ? $_GET['idCompte'] : '';
            if (empty($idCompte)) {
                jsonResponse(array("success" => false, "message" => "ID compte requis"));
            }
            
            $clientInfo = $clientManager->getByAccountId($idCompte);
            if ($clientInfo) {
                jsonResponse(array("success" => true, "data" => $clientInfo));
            } else {
                jsonResponse(array("success" => false, "message" => "Client non trouvé"));
            }
        } elseif ($_SERVER['REQUEST_METHOD'] == 'PUT') {
            $input = validateJsonInput();
            
            if (!isset($input['idClient'])) {
                jsonResponse(array("success" => false, "message" => "ID client requis"));
            }
            
            $result = $clientManager->update(
                $input['idClient'],
                $input['nom'] ?? '',
                $input['prenom'] ?? '',
                $input['numeroTelephone'] ?? '',
                $input['dateNaissance'] ?? null,
                $input['adresse'] ?? '',
                $input['sexe'] ?? 'Homme'
            );
            
            jsonResponse($result);
        }
        break;

    case 'demander_service':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            $required_fields = ['description', 'categorie', 'idClient'];
            foreach ($required_fields as $field) {
                if (!isset($input[$field]) || empty($input[$field])) {
                    jsonResponse(array("success" => false, "message" => "Le champ $field est requis"));
                }
            }
            
            $result = $demandeManager->create(
                $input['description'],
                $input['categorie'],
                $input['adresseTravail'] ?? '',
                $input['budget'] ?? null,
                $input['dateDebut'] ?? null,
                $input['dateFin'] ?? null,
                $input['idClient']
            );
            
            jsonResponse($result);
        }
        break;

    case 'mes_demandes':
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            $idClient = isset($_GET['idClient']) ? $_GET['idClient'] : '';
            if (empty($idClient)) {
                jsonResponse(array("success" => false, "message" => "ID client requis"));
            }
            
            $demandes = $demandeManager->getByClientId($idClient);
            jsonResponse(array("success" => true, "data" => $demandes));
        }
        break;

    case 'confirmer_fin_tache':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            if (!isset($input['idDemandeClient'])) {
                jsonResponse(array("success" => false, "message" => "ID demande requis"));
            }
            
            $result = $demandeManager->updateEtat($input['idDemandeClient'], 'termine');
            jsonResponse($result);
        }
        break;

    case 'evaluer_prestataire':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            $required_fields = ['idClient', 'idPrestataire', 'idDemandeClient', 'note'];
            foreach ($required_fields as $field) {
                if (!isset($input[$field])) {
                    jsonResponse(array("success" => false, "message" => "Le champ $field est requis"));
                }
            }
            
            // Créer l'évaluation
            $query = "INSERT INTO Evaluation (commentaire, note, idClient, idPrestataire, idDemandeClient) 
                      VALUES (:commentaire, :note, :idClient, :idPrestataire, :idDemandeClient)";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(":commentaire", $input['commentaire']);
            $stmt->bindParam(":note", $input['note']);
            $stmt->bindParam(":idClient", $input['idClient']);
            $stmt->bindParam(":idPrestataire", $input['idPrestataire']);
            $stmt->bindParam(":idDemandeClient", $input['idDemandeClient']);
            
            if ($stmt->execute()) {
                // Mettre à jour la note globale du prestataire
                $updateQuery = "UPDATE Prestataire SET 
                               noteGlobale = (SELECT AVG(note) FROM Evaluation WHERE idPrestataire = :idPrestataire),
                               nombreEvaluations = (SELECT COUNT(*) FROM Evaluation WHERE idPrestataire = :idPrestataire)
                               WHERE idPrestataire = :idPrestataire";
                
                $updateStmt = $db->prepare($updateQuery);
                $updateStmt->bindParam(":idPrestataire", $input['idPrestataire']);
                $updateStmt->execute();
                
                jsonResponse(array("success" => true, "message" => "Évaluation enregistrée avec succès"));
            } else {
                jsonResponse(array("success" => false, "message" => "Erreur lors de l'enregistrement de l'évaluation"));
            }
        }
        break;

    case 'poser_plainte':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            $required_fields = ['sujet', 'description', 'idClient', 'idPrestataire'];
            foreach ($required_fields as $field) {
                if (!isset($input[$field]) || empty($input[$field])) {
                    jsonResponse(array("success" => false, "message" => "Le champ $field est requis"));
                }
            }
            
            $query = "INSERT INTO PlainteClient (sujet, description, contenu, idClient, idPrestataire) 
                      VALUES (:sujet, :description, :contenu, :idClient, :idPrestataire)";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(":sujet", $input['sujet']);
            $stmt->bindParam(":description", $input['description']);
            $stmt->bindParam(":contenu", $input['contenu'] ?? '');
            $stmt->bindParam(":idClient", $input['idClient']);
            $stmt->bindParam(":idPrestataire", $input['idPrestataire']);
            
            if ($stmt->execute()) {
                jsonResponse(array("success" => true, "message" => "Plainte enregistrée avec succès"));
            } else {
                jsonResponse(array("success" => false, "message" => "Erreur lors de l'enregistrement de la plainte"));
            }
        }
        break;

    case 'consulter_devis':
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            $idDemandeClient = isset($_GET['idDemandeClient']) ? $_GET['idDemandeClient'] : '';
            if (empty($idDemandeClient)) {
                jsonResponse(array("success" => false, "message" => "ID demande requis"));
            }
            
            $query = "SELECT d.*, p.nom as prestataire_nom, p.prenom as prestataire_prenom 
                      FROM Devis d 
                      JOIN Prestataire p ON d.idPrestataire = p.idPrestataire 
                      WHERE d.idDemandeClient = :idDemandeClient";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(":idDemandeClient", $idDemandeClient);
            $stmt->execute();
            
            $devis = $stmt->fetchAll(PDO::FETCH_ASSOC);
            jsonResponse(array("success" => true, "data" => $devis));
        }
        break;

    case 'accepter_devis':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            if (!isset($input['idDevis']) || !isset($input['avisClient'])) {
                jsonResponse(array("success" => false, "message" => "ID devis et avis client requis"));
            }
            
            $query = "UPDATE Devis SET etat = 'accepte', avisClient = :avisClient WHERE idDevis = :idDevis";
            $stmt = $db->prepare($query);
            $stmt->bindParam(":avisClient", $input['avisClient']);
            $stmt->bindParam(":idDevis", $input['idDevis']);
            
            if ($stmt->execute()) {
                // Mettre à jour l'état de la demande
                $updateQuery = "UPDATE DemandeClient SET etat = 'accepte' 
                               WHERE idDemandeClient = (SELECT idDemandeClient FROM Devis WHERE idDevis = :idDevis)";
                $updateStmt = $db->prepare($updateQuery);
                $updateStmt->bindParam(":idDevis", $input['idDevis']);
                $updateStmt->execute();
                
                jsonResponse(array("success" => true, "message" => "Devis accepté avec succès"));
            } else {
                jsonResponse(array("success" => false, "message" => "Erreur lors de l'acceptation du devis"));
            }
        }
        break;

    case 'consulter_facture':
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            $idDemandeClient = isset($_GET['idDemandeClient']) ? $_GET['idDemandeClient'] : '';
            if (empty($idDemandeClient)) {
                jsonResponse(array("success" => false, "message" => "ID demande requis"));
            }
            
            $query = "SELECT f.*, dc.description as demande_description 
                      FROM Facture f 
                      JOIN DemandeClient dc ON f.idDemandeClient = dc.idDemandeClient 
                      WHERE f.idDemandeClient = :idDemandeClient";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(":idDemandeClient", $idDemandeClient);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $facture = $stmt->fetch(PDO::FETCH_ASSOC);
                jsonResponse(array("success" => true, "data" => $facture));
            } else {
                jsonResponse(array("success" => false, "message" => "Aucune facture trouvée"));
            }
        }
        break;

    default:
        jsonResponse(array("success" => false, "message" => "Action non reconnue"));
        break;
}
?>
