-- Script de création de la base de données pour la plateforme de services
-- Création de la base de données
CREATE DATABASE IF NOT EXISTS plateforme_services CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE plateforme_services;

-- Table Compte : informations de connexion
CREATE TABLE Compte (
    idCompte INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(50) NOT NULL UNIQUE,
    motDePasse VARCHAR(255) NOT NULL,
    typeCompte ENUM('client', 'prestataire', 'admin', 'service_client') NOT NULL,
    dateCreation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table Client : données personnelles du client
CREATE TABLE Client (
    idClient INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(50) NOT NULL,
    prenom VARCHAR(50) NOT NULL,
    numeroTelephone VARCHAR(15),
    dateNaissance DATE,
    adresse VARCHAR(100),
    sexe ENUM('Homme', 'Femme') DEFAULT 'Homme',
    idCompte INT NOT NULL,
    FOREIGN KEY (idCompte) REFERENCES Compte(idCompte) ON DELETE CASCADE
);

-- Table DemandePrestataire : demande d'inscription d'un prestataire
CREATE TABLE DemandePrestataire (
    idDemandePrestataire INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(50) NOT NULL,
    prenom VARCHAR(50) NOT NULL,
    dateNaissance DATE NOT NULL,
    numTel VARCHAR(15) NOT NULL,
    nin VARCHAR(20) NOT NULL UNIQUE,
    typeService VARCHAR(50) NOT NULL,
    adresse VARCHAR(100) NOT NULL,
    email VARCHAR(50) NOT NULL,
    cv LONGBLOB,
    etat ENUM('en_attente', 'accepte', 'refuse') DEFAULT 'en_attente',
    casierJudiciaire LONGBLOB,
    dateDemande TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Table Prestataire : données du prestataire de services
CREATE TABLE Prestataire (
    idPrestataire INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(50) NOT NULL,
    prenom VARCHAR(50) NOT NULL,
    numeroTelephone VARCHAR(15),
    dateNaissance DATE,
    adresse VARCHAR(100),
    sexe ENUM('Homme', 'Femme') DEFAULT 'Homme',
    estDisponible BOOLEAN DEFAULT TRUE,
    nin VARCHAR(20) NOT NULL UNIQUE,
    typeService VARCHAR(50) NOT NULL,
    cv LONGBLOB,
    casierJudiciaire LONGBLOB,
    noteGlobale DECIMAL(3,2) DEFAULT 0.00,
    nombreEvaluations INT DEFAULT 0,
    idCompte INT NOT NULL,
    idDemandePrestataire INT,
    FOREIGN KEY (idCompte) REFERENCES Compte(idCompte) ON DELETE CASCADE,
    FOREIGN KEY (idDemandePrestataire) REFERENCES DemandePrestataire(idDemandePrestataire)
);

-- Table DemandeClient : demande de prestation par un client
CREATE TABLE DemandeClient (
    idDemandeClient INT AUTO_INCREMENT PRIMARY KEY,
    dateDemande TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    dateDebut DATE,
    dateFin DATE,
    etat ENUM('en_attente', 'devis_propose', 'accepte', 'en_cours', 'termine', 'annule') DEFAULT 'en_attente',
    description TEXT NOT NULL,
    categorie VARCHAR(50) NOT NULL,
    adresseTravail VARCHAR(100),
    budget DECIMAL(10,2),
    idClient INT NOT NULL,
    idPrestataire INT,
    FOREIGN KEY (idClient) REFERENCES Client(idClient) ON DELETE CASCADE,
    FOREIGN KEY (idPrestataire) REFERENCES Prestataire(idPrestataire) ON DELETE SET NULL
);

-- Table MatierePremiere : catalogue des matières premières pour devis
CREATE TABLE MatierePremiere (
    idMatierePremiere INT AUTO_INCREMENT PRIMARY KEY,
    nomMat VARCHAR(50) NOT NULL,
    prixMat DECIMAL(10,2) NOT NULL,
    unite VARCHAR(20) DEFAULT 'unité'
);

-- Table Devis : estimation de coût proposée par un prestataire
CREATE TABLE Devis (
    idDevis INT AUTO_INCREMENT PRIMARY KEY,
    etat ENUM('en_attente', 'accepte', 'refuse', 'expire') DEFAULT 'en_attente',
    description TEXT,
    montantGlobal DECIMAL(10,2) NOT NULL,
    dateLimite DATE NOT NULL,
    avisClient TEXT,
    dateCreation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    idPrestataire INT NOT NULL,
    idDemandeClient INT NOT NULL,
    FOREIGN KEY (idPrestataire) REFERENCES Prestataire(idPrestataire) ON DELETE CASCADE,
    FOREIGN KEY (idDemandeClient) REFERENCES DemandeClient(idDemandeClient) ON DELETE CASCADE
);

-- Table QuantiteMatiereDevis : liaison devis ↔ matières concernées
CREATE TABLE QuantiteMatiereDevis (
    idDevis INT,
    idMatierePremiere INT,
    quantite INT NOT NULL DEFAULT 1,
    PRIMARY KEY(idDevis, idMatierePremiere),
    FOREIGN KEY (idDevis) REFERENCES Devis(idDevis) ON DELETE CASCADE,
    FOREIGN KEY (idMatierePremiere) REFERENCES MatierePremiere(idMatierePremiere) ON DELETE CASCADE
);

-- Table MatiereFacture : catalogue des matières utilisées sur facture
CREATE TABLE MatiereFacture (
    idMatiereFacture INT AUTO_INCREMENT PRIMARY KEY,
    nomMat VARCHAR(50) NOT NULL,
    prixMat DECIMAL(10,2) NOT NULL,
    unite VARCHAR(20) DEFAULT 'unité'
);

-- Table Facture : facture émise pour une demande client
CREATE TABLE Facture (
    idFacture INT AUTO_INCREMENT PRIMARY KEY,
    dateEmission TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    dateEcheance DATE NOT NULL,
    description TEXT,
    modePaiement ENUM('especes', 'carte', 'virement', 'cheque') DEFAULT 'especes',
    montantTotal DECIMAL(10,2) NOT NULL,
    etat ENUM('en_attente', 'payee', 'en_retard') DEFAULT 'en_attente',
    idDemandeClient INT NOT NULL,
    FOREIGN KEY (idDemandeClient) REFERENCES DemandeClient(idDemandeClient) ON DELETE CASCADE
);

-- Table QuantiteMatiereFacture : liaison factures ↔ matières facturées
CREATE TABLE QuantiteMatiereFacture (
    idFacture INT,
    idMatiereFacture INT,
    quantite INT NOT NULL DEFAULT 1,
    PRIMARY KEY(idFacture, idMatiereFacture),
    FOREIGN KEY (idFacture) REFERENCES Facture(idFacture) ON DELETE CASCADE,
    FOREIGN KEY (idMatiereFacture) REFERENCES MatiereFacture(idMatiereFacture) ON DELETE CASCADE
);

-- Table PlainteClient : réclamation déposée par un client
CREATE TABLE PlainteClient (
    idPlainteClient INT AUTO_INCREMENT PRIMARY KEY,
    sujet VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    contenu TEXT,
    etat ENUM('ouverte', 'en_cours', 'resolue', 'fermee') DEFAULT 'ouverte',
    idPrestataire INT NOT NULL,
    idClient INT NOT NULL,
    FOREIGN KEY (idPrestataire) REFERENCES Prestataire(idPrestataire) ON DELETE CASCADE,
    FOREIGN KEY (idClient) REFERENCES Client(idClient) ON DELETE CASCADE
);

-- Table PlaintePrestataire : plainte déposée par un prestataire
CREATE TABLE PlaintePrestataire (
    idPlaintePrestataire INT AUTO_INCREMENT PRIMARY KEY,
    sujet VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    contenu TEXT,
    etat ENUM('ouverte', 'en_cours', 'resolue', 'fermee') DEFAULT 'ouverte',
    idClient INT NOT NULL,
    idPrestataire INT NOT NULL,
    FOREIGN KEY (idClient) REFERENCES Client(idClient) ON DELETE CASCADE,
    FOREIGN KEY (idPrestataire) REFERENCES Prestataire(idPrestataire) ON DELETE CASCADE
);

-- Table Evaluation : retour client sur prestation
CREATE TABLE Evaluation (
    idEvaluation INT AUTO_INCREMENT PRIMARY KEY,
    commentaire TEXT,
    note DECIMAL(3,2) NOT NULL CHECK (note >= 0 AND note <= 5),
    image LONGBLOB,
    dateEvaluation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    idClient INT NOT NULL,
    idPrestataire INT NOT NULL,
    idDemandeClient INT NOT NULL,
    FOREIGN KEY (idClient) REFERENCES Client(idClient) ON DELETE CASCADE,
    FOREIGN KEY (idPrestataire) REFERENCES Prestataire(idPrestataire) ON DELETE CASCADE,
    FOREIGN KEY (idDemandeClient) REFERENCES DemandeClient(idDemandeClient) ON DELETE CASCADE
);

-- Table Diplome : diplomes obtenus par un prestataire
CREATE TABLE Diplome (
    idDiplome INT AUTO_INCREMENT PRIMARY KEY,
    institution VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    anneeObtention YEAR NOT NULL,
    fiche LONGBLOB,
    idPrestataire INT NOT NULL,
    FOREIGN KEY (idPrestataire) REFERENCES Prestataire(idPrestataire) ON DELETE CASCADE
);

-- Table DiplomeDemande : diplômes joints à une demande de prestataire
CREATE TABLE DiplomeDemande (
    idDiplomeDemande INT AUTO_INCREMENT PRIMARY KEY,
    institution VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL,
    anneeObtention YEAR NOT NULL,
    fiche LONGBLOB,
    idDemandePrestataire INT NOT NULL,
    FOREIGN KEY (idDemandePrestataire) REFERENCES DemandePrestataire(idDemandePrestataire) ON DELETE CASCADE
);

-- Table Admin : comptes administrateurs du système
CREATE TABLE Admin (
    idAdmin INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(50) NOT NULL,
    prenom VARCHAR(50) NOT NULL,
    numeroTelephone VARCHAR(15),
    dateNaissance DATE,
    adresse VARCHAR(100),
    sexe ENUM('Homme', 'Femme') DEFAULT 'Homme',
    idCompte INT NOT NULL,
    FOREIGN KEY (idCompte) REFERENCES Compte(idCompte) ON DELETE CASCADE
);

-- Table ServiceClient : comptes du service clientèle
CREATE TABLE ServiceClient (
    idServiceClient INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(50) NOT NULL,
    prenom VARCHAR(50) NOT NULL,
    numeroTelephone VARCHAR(15),
    dateNaissance DATE,
    adresse VARCHAR(100),
    sexe ENUM('Homme', 'Femme') DEFAULT 'Homme',
    idCompte INT NOT NULL,
    FOREIGN KEY (idCompte) REFERENCES Compte(idCompte) ON DELETE CASCADE
);

-- Insertion de données de test

-- Comptes de test
INSERT INTO Compte (email, motDePasse, typeCompte) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'admin'),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'service_client'),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'client'),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'client'),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'prestataire'),
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'prestataire');

-- Administrateurs
INSERT INTO Admin (nom, prenom, numeroTelephone, dateNaissance, adresse, sexe, idCompte) VALUES
('Dupont', 'Jean', '0123456789', '1980-05-15', '123 Rue de la Paix, Paris', 'Homme', 1);

-- Service Client
INSERT INTO ServiceClient (nom, prenom, numeroTelephone, dateNaissance, adresse, sexe, idCompte) VALUES
('Martin', 'Sophie', '0123456790', '1985-08-20', '456 Avenue des Champs, Lyon', 'Femme', 2);

-- Clients
INSERT INTO Client (nom, prenom, numeroTelephone, dateNaissance, adresse, sexe, idCompte) VALUES
('Durand', 'Pierre', '0123456791', '1990-03-10', '789 Boulevard Saint-Michel, Marseille', 'Homme', 3),
('Moreau', 'Marie', '0123456792', '1988-12-05', '321 Rue de Rivoli, Nice', 'Femme', 4);

-- Demandes de prestataires
INSERT INTO DemandePrestataire (nom, prenom, dateNaissance, numTel, nin, typeService, adresse, email, etat) VALUES
('Leroy', 'Antoine', '1985-07-12', '0123456793', '1234567890123', 'Plomberie', '654 Rue de la République, Toulouse', '<EMAIL>', 'accepte'),
('Bernard', 'Claire', '1987-11-25', '0123456794', '1234567890124', 'Électricité', '987 Avenue de la Liberté, Bordeaux', '<EMAIL>', 'accepte');

-- Prestataires
INSERT INTO Prestataire (nom, prenom, numeroTelephone, dateNaissance, adresse, sexe, estDisponible, nin, typeService, noteGlobale, nombreEvaluations, idCompte, idDemandePrestataire) VALUES
('Leroy', 'Antoine', '0123456793', '1985-07-12', '654 Rue de la République, Toulouse', 'Homme', TRUE, '1234567890123', 'Plomberie', 4.5, 10, 5, 1),
('Bernard', 'Claire', '0123456794', '1987-11-25', '987 Avenue de la Liberté, Bordeaux', 'Femme', TRUE, '1234567890124', 'Électricité', 4.8, 15, 6, 2);

-- Matières premières
INSERT INTO MatierePremiere (nomMat, prixMat, unite) VALUES
('Tuyau PVC 32mm', 15.50, 'mètre'),
('Coude PVC 32mm', 2.30, 'unité'),
('Câble électrique 2.5mm²', 3.20, 'mètre'),
('Disjoncteur 16A', 25.00, 'unité'),
('Prise électrique', 8.50, 'unité');

-- Matières de facturation
INSERT INTO MatiereFacture (nomMat, prixMat, unite) VALUES
('Tuyau PVC 32mm', 15.50, 'mètre'),
('Coude PVC 32mm', 2.30, 'unité'),
('Câble électrique 2.5mm²', 3.20, 'mètre'),
('Disjoncteur 16A', 25.00, 'unité'),
('Prise électrique', 8.50, 'unité');

-- Demandes clients
INSERT INTO DemandeClient (dateDebut, dateFin, etat, description, categorie, adresseTravail, budget, idClient, idPrestataire) VALUES
('2024-01-15', '2024-01-16', 'termine', 'Réparation fuite d\'eau dans la cuisine', 'Plomberie', '789 Boulevard Saint-Michel, Marseille', 150.00, 1, 1),
('2024-01-20', '2024-01-22', 'en_cours', 'Installation prises électriques salon', 'Électricité', '321 Rue de Rivoli, Nice', 200.00, 2, 2),
('2024-01-25', NULL, 'en_attente', 'Rénovation salle de bain complète', 'Plomberie', '789 Boulevard Saint-Michel, Marseille', 2000.00, 1, NULL);

-- Devis
INSERT INTO Devis (etat, description, montantGlobal, dateLimite, idPrestataire, idDemandeClient) VALUES
('accepte', 'Réparation fuite avec remplacement joint', 120.00, '2024-01-20', 1, 1),
('accepte', 'Installation 3 prises électriques avec câblage', 180.00, '2024-01-25', 2, 2),
('en_attente', 'Rénovation complète salle de bain', 1800.00, '2024-02-10', 1, 3);

-- Factures
INSERT INTO Facture (dateEcheance, description, modePaiement, montantTotal, etat, idDemandeClient) VALUES
('2024-01-30', 'Facture réparation fuite cuisine', 'carte', 120.00, 'payee', 1);

-- Évaluations
INSERT INTO Evaluation (commentaire, note, idClient, idPrestataire, idDemandeClient) VALUES
('Excellent travail, très professionnel et ponctuel', 5.0, 1, 1, 1),
('Bon travail mais un peu lent', 4.0, 2, 2, 2);
