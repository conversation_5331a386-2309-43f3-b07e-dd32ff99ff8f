<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, GET, PUT, DELETE, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Gestion des requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

class Database {
    private $host = "localhost";
    private $db_name = "plateforme_services";
    private $username = "root";
    private $password = "";
    public $conn;

    public function getConnection() {
        $this->conn = null;
        try {
            $this->conn = new PDO("mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=utf8mb4", 
                                $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $exception) {
            echo json_encode(array("message" => "Erreur de connexion: " . $exception->getMessage()));
        }
        return $this->conn;
    }
}

class Auth {
    private $conn;
    private $table_name = "Compte";

    public function __construct($db) {
        $this->conn = $db;
    }

    public function login($email, $password) {
        $query = "SELECT c.idCompte, c.email, c.typeCompte, c.motDePasse FROM " . $this->table_name . " c WHERE c.email = :email";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":email", $email);
        $stmt->execute();

        if($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            if(password_verify($password, $row['motDePasse'])) {
                return array(
                    "success" => true,
                    "idCompte" => $row['idCompte'],
                    "email" => $row['email'],
                    "typeCompte" => $row['typeCompte']
                );
            }
        }
        return array("success" => false, "message" => "Email ou mot de passe incorrect");
    }

    public function register($email, $password, $typeCompte = 'client') {
        // Vérifier si l'email existe déjà
        $check_query = "SELECT idCompte FROM " . $this->table_name . " WHERE email = :email";
        $check_stmt = $this->conn->prepare($check_query);
        $check_stmt->bindParam(":email", $email);
        $check_stmt->execute();

        if($check_stmt->rowCount() > 0) {
            return array("success" => false, "message" => "Cet email est déjà utilisé");
        }

        // Créer le compte
        $query = "INSERT INTO " . $this->table_name . " (email, motDePasse, typeCompte) VALUES (:email, :password, :typeCompte)";
        $stmt = $this->conn->prepare($query);
        
        $hashed_password = password_hash($password, PASSWORD_DEFAULT);
        $stmt->bindParam(":email", $email);
        $stmt->bindParam(":password", $hashed_password);
        $stmt->bindParam(":typeCompte", $typeCompte);

        if($stmt->execute()) {
            return array(
                "success" => true,
                "idCompte" => $this->conn->lastInsertId(),
                "message" => "Compte créé avec succès"
            );
        }
        return array("success" => false, "message" => "Erreur lors de la création du compte");
    }
}

class ClientManager {
    private $conn;
    private $table_name = "Client";

    public function __construct($db) {
        $this->conn = $db;
    }

    public function create($nom, $prenom, $numeroTelephone, $dateNaissance, $adresse, $sexe, $idCompte) {
        $query = "INSERT INTO " . $this->table_name . " 
                  (nom, prenom, numeroTelephone, dateNaissance, adresse, sexe, idCompte) 
                  VALUES (:nom, :prenom, :numeroTelephone, :dateNaissance, :adresse, :sexe, :idCompte)";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":nom", $nom);
        $stmt->bindParam(":prenom", $prenom);
        $stmt->bindParam(":numeroTelephone", $numeroTelephone);
        $stmt->bindParam(":dateNaissance", $dateNaissance);
        $stmt->bindParam(":adresse", $adresse);
        $stmt->bindParam(":sexe", $sexe);
        $stmt->bindParam(":idCompte", $idCompte);

        if($stmt->execute()) {
            return array("success" => true, "idClient" => $this->conn->lastInsertId());
        }
        return array("success" => false, "message" => "Erreur lors de la création du profil client");
    }

    public function getByAccountId($idCompte) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE idCompte = :idCompte";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":idCompte", $idCompte);
        $stmt->execute();

        if($stmt->rowCount() > 0) {
            return $stmt->fetch(PDO::FETCH_ASSOC);
        }
        return null;
    }

    public function update($idClient, $nom, $prenom, $numeroTelephone, $dateNaissance, $adresse, $sexe) {
        $query = "UPDATE " . $this->table_name . " 
                  SET nom = :nom, prenom = :prenom, numeroTelephone = :numeroTelephone, 
                      dateNaissance = :dateNaissance, adresse = :adresse, sexe = :sexe 
                  WHERE idClient = :idClient";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":nom", $nom);
        $stmt->bindParam(":prenom", $prenom);
        $stmt->bindParam(":numeroTelephone", $numeroTelephone);
        $stmt->bindParam(":dateNaissance", $dateNaissance);
        $stmt->bindParam(":adresse", $adresse);
        $stmt->bindParam(":sexe", $sexe);
        $stmt->bindParam(":idClient", $idClient);

        if($stmt->execute()) {
            return array("success" => true, "message" => "Profil mis à jour avec succès");
        }
        return array("success" => false, "message" => "Erreur lors de la mise à jour");
    }
}

class DemandeClientManager {
    private $conn;
    private $table_name = "DemandeClient";

    public function __construct($db) {
        $this->conn = $db;
    }

    public function create($description, $categorie, $adresseTravail, $budget, $dateDebut, $dateFin, $idClient) {
        $query = "INSERT INTO " . $this->table_name . " 
                  (description, categorie, adresseTravail, budget, dateDebut, dateFin, idClient) 
                  VALUES (:description, :categorie, :adresseTravail, :budget, :dateDebut, :dateFin, :idClient)";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":description", $description);
        $stmt->bindParam(":categorie", $categorie);
        $stmt->bindParam(":adresseTravail", $adresseTravail);
        $stmt->bindParam(":budget", $budget);
        $stmt->bindParam(":dateDebut", $dateDebut);
        $stmt->bindParam(":dateFin", $dateFin);
        $stmt->bindParam(":idClient", $idClient);

        if($stmt->execute()) {
            return array("success" => true, "idDemandeClient" => $this->conn->lastInsertId());
        }
        return array("success" => false, "message" => "Erreur lors de la création de la demande");
    }

    public function getByClientId($idClient) {
        $query = "SELECT dc.*, p.nom as prestataire_nom, p.prenom as prestataire_prenom, p.numeroTelephone as prestataire_tel
                  FROM " . $this->table_name . " dc 
                  LEFT JOIN Prestataire p ON dc.idPrestataire = p.idPrestataire 
                  WHERE dc.idClient = :idClient 
                  ORDER BY dc.dateDemande DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":idClient", $idClient);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getAll() {
        $query = "SELECT dc.*, c.nom as client_nom, c.prenom as client_prenom, 
                         p.nom as prestataire_nom, p.prenom as prestataire_prenom
                  FROM " . $this->table_name . " dc 
                  LEFT JOIN Client c ON dc.idClient = c.idClient
                  LEFT JOIN Prestataire p ON dc.idPrestataire = p.idPrestataire 
                  ORDER BY dc.dateDemande DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function updateEtat($idDemandeClient, $etat) {
        $query = "UPDATE " . $this->table_name . " SET etat = :etat WHERE idDemandeClient = :idDemandeClient";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":etat", $etat);
        $stmt->bindParam(":idDemandeClient", $idDemandeClient);

        if($stmt->execute()) {
            return array("success" => true, "message" => "État mis à jour avec succès");
        }
        return array("success" => false, "message" => "Erreur lors de la mise à jour");
    }

    public function assignPrestataire($idDemandeClient, $idPrestataire) {
        $query = "UPDATE " . $this->table_name . " 
                  SET idPrestataire = :idPrestataire, etat = 'accepte' 
                  WHERE idDemandeClient = :idDemandeClient";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":idPrestataire", $idPrestataire);
        $stmt->bindParam(":idDemandeClient", $idDemandeClient);

        if($stmt->execute()) {
            return array("success" => true, "message" => "Prestataire assigné avec succès");
        }
        return array("success" => false, "message" => "Erreur lors de l'assignation");
    }
}

// Fonction utilitaire pour valider les données JSON
function validateJsonInput() {
    $input = json_decode(file_get_contents("php://input"), true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        echo json_encode(array("success" => false, "message" => "Données JSON invalides"));
        exit();
    }
    return $input;
}

// Fonction utilitaire pour répondre en JSON
function jsonResponse($data) {
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit();
}
?>
