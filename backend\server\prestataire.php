<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, GET, PUT, DELETE, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Gestion des requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once 'db.php';

$database = new Database();
$db = $database->getConnection();

$auth = new Auth($db);

// Récupérer l'action depuis l'URL ou POST
$action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

switch($action) {
    case 'login':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            if (!isset($input['email']) || !isset($input['password'])) {
                jsonResponse(array("success" => false, "message" => "Email et mot de passe requis"));
            }
            
            $result = $auth->login($input['email'], $input['password']);
            
            if ($result['success'] && $result['typeCompte'] == 'prestataire') {
                // Récupérer les informations du prestataire
                $query = "SELECT * FROM Prestataire WHERE idCompte = :idCompte";
                $stmt = $db->prepare($query);
                $stmt->bindParam(":idCompte", $result['idCompte']);
                $stmt->execute();
                
                if ($stmt->rowCount() > 0) {
                    $prestataireInfo = $stmt->fetch(PDO::FETCH_ASSOC);
                    $result['prestataireInfo'] = $prestataireInfo;
                }
                jsonResponse($result);
            } else {
                jsonResponse(array("success" => false, "message" => "Accès non autorisé pour ce type de compte"));
            }
        }
        break;

    case 'demande_inscription':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            $required_fields = ['nom', 'prenom', 'dateNaissance', 'numTel', 'nin', 'typeService', 'adresse', 'email'];
            foreach ($required_fields as $field) {
                if (!isset($input[$field]) || empty($input[$field])) {
                    jsonResponse(array("success" => false, "message" => "Le champ $field est requis"));
                }
            }
            
            $query = "INSERT INTO DemandePrestataire 
                      (nom, prenom, dateNaissance, numTel, nin, typeService, adresse, email) 
                      VALUES (:nom, :prenom, :dateNaissance, :numTel, :nin, :typeService, :adresse, :email)";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(":nom", $input['nom']);
            $stmt->bindParam(":prenom", $input['prenom']);
            $stmt->bindParam(":dateNaissance", $input['dateNaissance']);
            $stmt->bindParam(":numTel", $input['numTel']);
            $stmt->bindParam(":nin", $input['nin']);
            $stmt->bindParam(":typeService", $input['typeService']);
            $stmt->bindParam(":adresse", $input['adresse']);
            $stmt->bindParam(":email", $input['email']);
            
            if ($stmt->execute()) {
                jsonResponse(array("success" => true, "message" => "Demande d'inscription soumise avec succès"));
            } else {
                jsonResponse(array("success" => false, "message" => "Erreur lors de la soumission de la demande"));
            }
        }
        break;

    case 'profile':
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            $idPrestataire = isset($_GET['idPrestataire']) ? $_GET['idPrestataire'] : '';
            if (empty($idPrestataire)) {
                jsonResponse(array("success" => false, "message" => "ID prestataire requis"));
            }
            
            $query = "SELECT * FROM Prestataire WHERE idPrestataire = :idPrestataire";
            $stmt = $db->prepare($query);
            $stmt->bindParam(":idPrestataire", $idPrestataire);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $prestataire = $stmt->fetch(PDO::FETCH_ASSOC);
                jsonResponse(array("success" => true, "data" => $prestataire));
            } else {
                jsonResponse(array("success" => false, "message" => "Prestataire non trouvé"));
            }
        } elseif ($_SERVER['REQUEST_METHOD'] == 'PUT') {
            $input = validateJsonInput();
            
            if (!isset($input['idPrestataire'])) {
                jsonResponse(array("success" => false, "message" => "ID prestataire requis"));
            }
            
            $query = "UPDATE Prestataire SET 
                      nom = :nom, prenom = :prenom, numeroTelephone = :numeroTelephone, 
                      dateNaissance = :dateNaissance, adresse = :adresse, sexe = :sexe 
                      WHERE idPrestataire = :idPrestataire";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(":nom", $input['nom']);
            $stmt->bindParam(":prenom", $input['prenom']);
            $stmt->bindParam(":numeroTelephone", $input['numeroTelephone']);
            $stmt->bindParam(":dateNaissance", $input['dateNaissance']);
            $stmt->bindParam(":adresse", $input['adresse']);
            $stmt->bindParam(":sexe", $input['sexe']);
            $stmt->bindParam(":idPrestataire", $input['idPrestataire']);
            
            if ($stmt->execute()) {
                jsonResponse(array("success" => true, "message" => "Profil mis à jour avec succès"));
            } else {
                jsonResponse(array("success" => false, "message" => "Erreur lors de la mise à jour"));
            }
        }
        break;

    case 'basculer_disponibilite':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            if (!isset($input['idPrestataire'])) {
                jsonResponse(array("success" => false, "message" => "ID prestataire requis"));
            }
            
            $query = "UPDATE Prestataire SET estDisponible = NOT estDisponible WHERE idPrestataire = :idPrestataire";
            $stmt = $db->prepare($query);
            $stmt->bindParam(":idPrestataire", $input['idPrestataire']);
            
            if ($stmt->execute()) {
                // Récupérer le nouvel état
                $checkQuery = "SELECT estDisponible FROM Prestataire WHERE idPrestataire = :idPrestataire";
                $checkStmt = $db->prepare($checkQuery);
                $checkStmt->bindParam(":idPrestataire", $input['idPrestataire']);
                $checkStmt->execute();
                $result = $checkStmt->fetch(PDO::FETCH_ASSOC);
                
                jsonResponse(array(
                    "success" => true, 
                    "message" => "Disponibilité mise à jour",
                    "estDisponible" => (bool)$result['estDisponible']
                ));
            } else {
                jsonResponse(array("success" => false, "message" => "Erreur lors de la mise à jour"));
            }
        }
        break;

    case 'demandes_disponibles':
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            $typeService = isset($_GET['typeService']) ? $_GET['typeService'] : '';
            
            $query = "SELECT dc.*, c.nom as client_nom, c.prenom as client_prenom, c.numeroTelephone as client_tel
                      FROM DemandeClient dc 
                      JOIN Client c ON dc.idClient = c.idClient 
                      WHERE dc.etat = 'en_attente'";
            
            if (!empty($typeService)) {
                $query .= " AND dc.categorie = :typeService";
            }
            
            $query .= " ORDER BY dc.dateDemande DESC";
            
            $stmt = $db->prepare($query);
            if (!empty($typeService)) {
                $stmt->bindParam(":typeService", $typeService);
            }
            $stmt->execute();
            
            $demandes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            jsonResponse(array("success" => true, "data" => $demandes));
        }
        break;

    case 'proposer_devis':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            $required_fields = ['idPrestataire', 'idDemandeClient', 'montantGlobal', 'dateLimite'];
            foreach ($required_fields as $field) {
                if (!isset($input[$field])) {
                    jsonResponse(array("success" => false, "message" => "Le champ $field est requis"));
                }
            }
            
            $query = "INSERT INTO Devis (description, montantGlobal, dateLimite, idPrestataire, idDemandeClient) 
                      VALUES (:description, :montantGlobal, :dateLimite, :idPrestataire, :idDemandeClient)";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(":description", $input['description']);
            $stmt->bindParam(":montantGlobal", $input['montantGlobal']);
            $stmt->bindParam(":dateLimite", $input['dateLimite']);
            $stmt->bindParam(":idPrestataire", $input['idPrestataire']);
            $stmt->bindParam(":idDemandeClient", $input['idDemandeClient']);
            
            if ($stmt->execute()) {
                // Mettre à jour l'état de la demande
                $updateQuery = "UPDATE DemandeClient SET etat = 'devis_propose' WHERE idDemandeClient = :idDemandeClient";
                $updateStmt = $db->prepare($updateQuery);
                $updateStmt->bindParam(":idDemandeClient", $input['idDemandeClient']);
                $updateStmt->execute();
                
                jsonResponse(array("success" => true, "message" => "Devis proposé avec succès"));
            } else {
                jsonResponse(array("success" => false, "message" => "Erreur lors de la proposition du devis"));
            }
        }
        break;

    case 'mes_prises_en_charge':
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            $idPrestataire = isset($_GET['idPrestataire']) ? $_GET['idPrestataire'] : '';
            if (empty($idPrestataire)) {
                jsonResponse(array("success" => false, "message" => "ID prestataire requis"));
            }
            
            $query = "SELECT dc.*, c.nom as client_nom, c.prenom as client_prenom, c.numeroTelephone as client_tel,
                             d.montantGlobal as devis_montant, d.etat as devis_etat
                      FROM DemandeClient dc 
                      JOIN Client c ON dc.idClient = c.idClient 
                      LEFT JOIN Devis d ON dc.idDemandeClient = d.idDemandeClient AND d.idPrestataire = :idPrestataire
                      WHERE dc.idPrestataire = :idPrestataire 
                      ORDER BY dc.dateDemande DESC";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(":idPrestataire", $idPrestataire);
            $stmt->execute();
            
            $prises_en_charge = $stmt->fetchAll(PDO::FETCH_ASSOC);
            jsonResponse(array("success" => true, "data" => $prises_en_charge));
        }
        break;

    case 'mettre_a_jour_avancement':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            if (!isset($input['idDemandeClient']) || !isset($input['etat'])) {
                jsonResponse(array("success" => false, "message" => "ID demande et état requis"));
            }
            
            $query = "UPDATE DemandeClient SET etat = :etat WHERE idDemandeClient = :idDemandeClient";
            $stmt = $db->prepare($query);
            $stmt->bindParam(":etat", $input['etat']);
            $stmt->bindParam(":idDemandeClient", $input['idDemandeClient']);
            
            if ($stmt->execute()) {
                jsonResponse(array("success" => true, "message" => "Avancement mis à jour avec succès"));
            } else {
                jsonResponse(array("success" => false, "message" => "Erreur lors de la mise à jour"));
            }
        }
        break;

    case 'poser_plainte':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            $required_fields = ['sujet', 'description', 'idPrestataire', 'idClient'];
            foreach ($required_fields as $field) {
                if (!isset($input[$field]) || empty($input[$field])) {
                    jsonResponse(array("success" => false, "message" => "Le champ $field est requis"));
                }
            }
            
            $query = "INSERT INTO PlaintePrestataire (sujet, description, contenu, idPrestataire, idClient) 
                      VALUES (:sujet, :description, :contenu, :idPrestataire, :idClient)";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(":sujet", $input['sujet']);
            $stmt->bindParam(":description", $input['description']);
            $stmt->bindParam(":contenu", $input['contenu'] ?? '');
            $stmt->bindParam(":idPrestataire", $input['idPrestataire']);
            $stmt->bindParam(":idClient", $input['idClient']);
            
            if ($stmt->execute()) {
                jsonResponse(array("success" => true, "message" => "Plainte enregistrée avec succès"));
            } else {
                jsonResponse(array("success" => false, "message" => "Erreur lors de l'enregistrement de la plainte"));
            }
        }
        break;

    case 'mes_evaluations':
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            $idPrestataire = isset($_GET['idPrestataire']) ? $_GET['idPrestataire'] : '';
            if (empty($idPrestataire)) {
                jsonResponse(array("success" => false, "message" => "ID prestataire requis"));
            }
            
            $query = "SELECT e.*, c.nom as client_nom, c.prenom as client_prenom, dc.description as demande_description
                      FROM Evaluation e 
                      JOIN Client c ON e.idClient = c.idClient 
                      JOIN DemandeClient dc ON e.idDemandeClient = dc.idDemandeClient
                      WHERE e.idPrestataire = :idPrestataire 
                      ORDER BY e.dateEvaluation DESC";
            
            $stmt = $db->prepare($query);
            $stmt->bindParam(":idPrestataire", $idPrestataire);
            $stmt->execute();
            
            $evaluations = $stmt->fetchAll(PDO::FETCH_ASSOC);
            jsonResponse(array("success" => true, "data" => $evaluations));
        }
        break;

    default:
        jsonResponse(array("success" => false, "message" => "Action non reconnue"));
        break;
}
?>
