# Présentation du Projet Web

## 1. Description générale du projet

Avec l’augmentation continue de l’utilisation d’Internet et des technologies modernes dans divers domaines, le besoin de plateformes efficaces pour connecter les utilisateurs aux prestataires de services devient de plus en plus crucial. Cette application web propose une solution pour mettre en relation des clients ayant besoin de services (plomberie, électricité, ménage, réparation électronique, cuisine, coiffure, etc.) avec des professionnels qualifiés. Elle vise à simplifier la demande de services, renforcer la fiabilité, et offrir de nouvelles opportunités de travail pour les prestataires, tout en assurant une gestion centralisée garantissant la qualité des prestations.  
> **Contexte** : Dans un monde où le travail indépendant prend une place grandissante, les plateformes numériques jouent un rôle clé pour offrir une vitrine aux prestataires et un espace sécurisé pour les clients. fileciteturn1file10

---

## 2. Modèle relationnel

```sql
-- Compte : informations de connexion
Compte(
  idCompte PK,
  email VARCHAR(50) NOT NULL,
  motDePasse VARCHAR(255) NOT NULL
);

-- Client : données personnelles du client
Client(
  idClient PK,
  nom VARCHAR(50),
  prenom VARCHAR(50),
  numeroTelephone VARCHAR(15),
  dateNaissance DATE,
  adresse VARCHAR(100),
  sexe VARCHAR(10),
  idCompte FK → Compte(idCompte)
);

-- Prestataire : données du prestataire de services
Prestataire(
  idPrestataire PK,
  nom VARCHAR(50),
  prenom VARCHAR(50),
  numeroTelephone VARCHAR(15),
  dateNaissance DATE,
  adresse VARCHAR(100),
  sexe VARCHAR(10),
  estDisponible BOOLEAN,
  nin VARCHAR(20),
  typeService VARCHAR(50),
  cv BLOB,
  casierJudiciaire BLOB,
  idCompte FK → Compte(idCompte),
  idDemandePrestataire FK → DemandePrestataire(idDemandePrestataire)
);

-- DemandeClient : demande de prestation par un client
DemandeClient(
  idDemandeClient PK,
  dateDemande DATE,
  dateDebut DATE,
  dateFin DATE,
  etat VARCHAR(20),
  description TEXT,
  categorie VARCHAR(50),
  idClient FK → Client(idClient),
  idPrestataire FK → Prestataire(idPrestataire)
);

-- Facture : facture émise pour une demande client
Facture(
  idFacture PK,
  dateEmission DATE,
  dateEcheance DATE,
  description TEXT,
  modePaiement VARCHAR(30),
  idDemandeClient FK → DemandeClient(idDemandeClient)
);

-- QuantiteMatiereFacture : liaison factures ↔ matières facturées
QuantiteMatiereFacture(
  idFacture FK → Facture(idFacture),
  idMatiereFacture FK → MatiereFacture(idMatiereFacture),
  quantite INT,
  PRIMARY KEY(idFacture, idMatiereFacture)
);

-- MatiereFacture : catalogue des matières utilisées sur facture
MatiereFacture(
  idMatiereFacture PK,
  nomMat VARCHAR(50),
  prixMat DECIMAL
);

-- Devis : estimation de coût proposée par un prestataire
Devis(
  idDevis PK,
  etat VARCHAR(20),
  description TEXT,
  montantGlobal DECIMAL,
  dateLimite DATE,
  avisClient TEXT,
  idPrestataire FK → Prestataire(idPrestataire),
  idDemandeClient FK → DemandeClient(idDemandeClient)
);

-- QuantiteMatiereDevis : liaison devis ↔ matières concernées
QuantiteMatiereDevis(
  idDevis FK → Devis(idDevis),
  idMatierePremiere FK → MatierePremiere(idMatierePremiere),
  quantite INT,
  PRIMARY KEY(idDevis, idMatierePremiere)
);

-- MatierePremiere : catalogue des matières premières pour devis
MatierePremiere(
  idMatierePremiere PK,
  nomMat VARCHAR(50),
  prixMat DECIMAL
);

-- PlainteClient : réclamation déposée par un client
PlainteClient(
  idPlainteClient PK,
  sujet VARCHAR(100),
  description TEXT,
  date DATE,
  contenu TEXT,
  idPrestataire FK → Prestataire(idPrestataire),
  idClient FK → Client(idClient)
);

-- PlaintePrestataire : plainte déposée par un prestataire
PlaintePrestataire(
  idPlaintePrestataire PK,
  sujet VARCHAR(100),
  description TEXT,
  date DATE,
  contenu TEXT,
  idClient FK → Client(idClient),
  idPrestataire FK → Prestataire(idPrestataire)
);

-- Evaluation : retour client sur prestation
Evaluation(
  idEvaluation PK,
  commentaire TEXT,
  note DECIMAL,
  image BLOB,
  idClient FK → Client(idClient),
  idPrestataire FK → Prestataire(idPrestataire)
);

-- Diplome : diplomes obtenus par un prestataire
Diplome(
  idDiplome PK,
  institution VARCHAR(100),
  type VARCHAR(50),
  anneeObtention YEAR,
  fiche BLOB,
  idPrestataire FK → Prestataire(idPrestataire)
);

-- DemandePrestataire : demande d’inscription d’un prestataire
DemandePrestataire(
  idDemandePrestataire PK,
  nom VARCHAR(50),
  prenom VARCHAR(50),
  dateNaissance DATE,
  numTel VARCHAR(15),
  nin VARCHAR(20),
  typeService VARCHAR(50),
  adresse VARCHAR(100),
  email VARCHAR(50),
  cv BLOB,
  etat VARCHAR(20),
  casierJudiciaire BLOB
);

-- DiplomeDemande : diplômes joints à une demande de prestataire
DiplomeDemande(
  idDiplomeDemande PK,
  institution VARCHAR(100),
  type VARCHAR(50),
  anneeObtention YEAR,
  fiche BLOB,
  idDemandePrestataire FK → DemandePrestataire(idDemandePrestataire)
);

-- Admin : comptes administrateurs du système
Admin(
  idAdmin PK,
  nom VARCHAR(50),
  prenom VARCHAR(50),
  numeroTelephone VARCHAR(15),
  dateNaissance DATE,
  adresse VARCHAR(100),
  sexe VARCHAR(10),
  idCompte FK → Compte(idCompte)
);

-- ServiceClient : comptes du service clientèle
ServiceClient(
  idServiceClient PK,
  nom VARCHAR(50),
  prenom VARCHAR(50),
  numeroTelephone VARCHAR(15),
  dateNaissance DATE,
  adresse VARCHAR(100),
  sexe VARCHAR(10),
  idCompte FK → Compte(idCompte)
);

```
(Définition complète des tables et dictionnaire de données fileciteturn1file0turn1file1)

---

## 3. Classes (Modèle objet)

- **Compte**  
- **Client**  
- **Prestataire**  
- **DemandeClient**  
- **Facture**  
- **MatiereFacture**, **QuantiteMatiereFacture**  
- **Devis**, **QuantiteMatiereDevis**, **MatierePremiere**  
- **PlainteClient**, **PlaintePrestataire**  
- **Evaluation**  
- **Diplome**, **DiplomeDemande**  
- **DemandePrestataire**  
- **Admin**, **ServiceClient** fileciteturn1file0

---

## 4. Architecture et structure des dossiers

```
project/
│
├── frontend/
│   ├── model/
│   │   └── ClasseX.js       # classes métier (models)
│   ├── view/
│   │   ├── usecaseA/        # cas d’utilisation A
│   │   │   ├── index.html
│   │   │   ├── style.css
│   │   │   └── script.js
│   │   └── usecaseB/        # cas d’utilisation B
│   └── controller/
│       └── usecaseA.js      # logique d’interaction, appels aux models
│
└── backend/
    ├── database.sql         # script de création et d’insertions SQL
    └── server/
        ├── db.php           # connexion MySQL et fonctions CRUD
        ├── client.php       # endpoints pour les clients
        └── prestataire.php  # endpoints pour les prestataires
```

- **Technologies** : HTML, CSS, JavaScript, PHP, MySQL  
- **Langue officielle** : français pour la documentation, le code et l’interface utilisateur  
- **Intégrité des données** : clés primaires/étrangères, validations côté serveur et client  
- **UI** : moderne et responsive pour une expérience utilisateur optimale  

---

## 5. Description des cas d’utilisation (scénarios)

## Cas d’utilisation (Résumé)

1. **Se connecter**  
   - **Acteurs** : Client / Prestataire / Service Clientèle  
   - **But** : Authentifier et accéder au tableau de bord  
   - **Pré/post** : Formulaire accessible → redirection vers l’espace  

2. **S’inscrire (client)**  
   - **Acteur** : Nouvel utilisateur  
   - **But** : Créer un compte client  
   - **Pré/post** : Formulaire disponible → compte créé  

3. **Profil client**  
   - **Acteur** : Client  
   - **But** : Afficher/mettre à jour ses données  
   - **Pré/post** : Authentifié → profil mis à jour  

4. **Demander un service**  
   - **Acteur** : Client  
   - **But** : Enregistrer une nouvelle demande  
   - **Pré/post** : Connecté → demande “en attente”  

5. **Consulter un devis**  
   - **Acteur** : Client  
   - **But** : Voir et accepter un devis  
   - **Pré/post** : Devis dispo → devis signé  

6. **Confirmer fin de tâche**  
   - **Acteur** : Client  
   - **But** : Valider l’achèvement et noter  
   - **Pré/post** : Tâche terminée → évaluation disponible  

7. **Poser une plainte**  
   - **Acteur** : Client  
   - **But** : Signaler un problème  
   - **Pré/post** : Demande en cours → plainte enregistrée  

8. **Consulter facture**  
   - **Acteur** : Client  
   - **But** : Afficher détail de la facture  
   - **Pré/post** : Facture générée → facture affichée  

9. **Suivre mes demandes**  
   - **Acteur** : Client  
   - **But** : Vérifier le statut de chaque demande  
   - **Pré/post** : Demandes existantes → statuts affichés  

10. **Gérer inscriptions prestataires**  
    - **Acteur** : Service Clientèle  
    - **But** : Accepter ou refuser une demande  
    - **Pré/post** : Demande en attente → statut mis à jour  

11. **Affecter prestataire**  
    - **Acteur** : Service Clientèle  
    - **But** : Assigner un prestataire à une demande  
    - **Pré/post** : Devis accepté → affectation créée  

12. **Liste des prestataires**  
    - **Acteur** : Service Clientèle  
    - **But** : Voir prestataires validés/bloqués  
    - **Pré/post** : Base accessible → liste affichée  

13. **État prise en charge**  
    - **Acteur** : Prestataire  
    - **But** : Voir l’avancement d’une tâche  
    - **Pré/post** : Prise en charge active → détail affiché  

14. **Proposer un devis**  
    - **Acteur** : Prestataire  
    - **But** : Créer un devis pour une demande  
    - **Pré/post** : Demande en attente → devis enregistré  

15. **Profil prestataire**  
    - **Acteur** : Prestataire  
    - **But** : Afficher/mettre à jour ses données  
    - **Pré/post** : Connecté → profil mis à jour  

16. **Basculer disponibilité**  
    - **Acteur** : Prestataire  
    - **But** : Passer disponible ↔ indisponible  
    - **Pré/post** : Connecté → statut mis à jour  

17. **Mettre à jour avancement**  
    - **Acteur** : Prestataire  
    - **But** : Actualiser % d’avancement (+ plainte)  
    - **Pré/post** : Prise en charge active → état enregistré  

18. **Consulter la black-liste**  
    - **Acteur** : Administrateur  
    - **But** : Voir prestataires bloqués  
    - **Pré/post** : Admin connecté → black-liste affichée  

