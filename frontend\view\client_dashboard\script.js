// Variables globales
let currentUser = null;
let currentDemandes = [];
let currentDevis = [];

// Initialisation
document.addEventListener('DOMContentLoaded', function() {
    // Vérifier l'authentification
    checkAuth();
    
    // Configurer les gestionnaires d'événements
    setupEventListeners();
    
    // Charger les données initiales
    loadDashboardData();
});

// Vérification de l'authentification
function checkAuth() {
    const savedUser = localStorage.getItem('currentUser');
    if (!savedUser) {
        window.location.href = '../../index.html';
        return;
    }
    
    currentUser = JSON.parse(savedUser);
    if (currentUser.typeCompte !== 'client') {
        window.location.href = '../../index.html';
        return;
    }
    
    // Mettre à jour l'interface utilisateur
    document.getElementById('user-name').textContent = currentUser.email;
}

// Configuration des gestionnaires d'événements
function setupEventListeners() {
    // Formulaire de demande de service
    document.getElementById('service-request-form').addEventListener('submit', handleServiceRequest);
    
    // Formulaire de profil
    document.getElementById('profile-form').addEventListener('submit', handleProfileUpdate);
    
    // Formulaire de plainte
    document.getElementById('complaint-form').addEventListener('submit', handleComplaintSubmit);
    
    // Formulaire d'évaluation
    document.getElementById('evaluation-form').addEventListener('submit', handleEvaluationSubmit);
    
    // Étoiles de notation
    setupRatingStars();
}

// Navigation entre sections
function showSection(sectionName) {
    // Masquer toutes les sections
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // Afficher la section demandée
    document.getElementById(sectionName).classList.add('active');
    
    // Mettre à jour la navigation
    document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
    });
    
    // Activer l'élément de navigation correspondant
    const activeNavItem = document.querySelector(`[onclick="showSection('${sectionName}')"]`);
    if (activeNavItem) {
        activeNavItem.classList.add('active');
    }
    
    // Mettre à jour le titre de la page
    const titles = {
        'dashboard': 'Tableau de bord',
        'demander-service': 'Demander un service',
        'mes-demandes': 'Mes demandes',
        'devis': 'Devis',
        'factures': 'Factures',
        'evaluations': 'Évaluations',
        'plaintes': 'Plaintes',
        'profil': 'Mon profil'
    };
    
    document.getElementById('page-title').textContent = titles[sectionName] || 'ServicePro';
    
    // Charger les données spécifiques à la section
    loadSectionData(sectionName);
}

// Chargement des données du tableau de bord
async function loadDashboardData() {
    try {
        // Charger les demandes du client
        await loadMesDemandes();
        
        // Charger le profil
        await loadProfile();
        
        // Mettre à jour les statistiques
        updateDashboardStats();
        
        // Charger l'activité récente
        loadRecentActivity();
        
    } catch (error) {
        console.error('Erreur lors du chargement des données:', error);
        showMessage('Erreur lors du chargement des données', 'error');
    }
}

// Chargement des données spécifiques à une section
async function loadSectionData(sectionName) {
    switch(sectionName) {
        case 'mes-demandes':
            await loadMesDemandes();
            break;
        case 'devis':
            await loadDevis();
            break;
        case 'factures':
            await loadFactures();
            break;
        case 'evaluations':
            await loadEvaluations();
            break;
        case 'plaintes':
            await loadPlaintes();
            break;
        case 'profil':
            await loadProfile();
            break;
    }
}

// Gestion de la demande de service
async function handleServiceRequest(event) {
    event.preventDefault();
    
    const formData = {
        description: document.getElementById('service-description').value,
        categorie: document.getElementById('service-category').value,
        adresseTravail: document.getElementById('service-address').value,
        budget: document.getElementById('service-budget').value || null,
        dateDebut: document.getElementById('service-date-debut').value || null,
        dateFin: document.getElementById('service-date-fin').value || null,
        idClient: currentUser.clientInfo.idClient
    };
    
    try {
        const response = await fetch(`${ServiceProApp.API_BASE_URL}/client.php?action=demander_service`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('Demande de service soumise avec succès !', 'success');
            document.getElementById('service-request-form').reset();
            
            // Recharger les demandes
            await loadMesDemandes();
            updateDashboardStats();
            
            // Rediriger vers mes demandes
            showSection('mes-demandes');
        } else {
            showMessage(result.message || 'Erreur lors de la soumission', 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur de connexion au serveur', 'error');
    }
}

// Chargement des demandes du client
async function loadMesDemandes() {
    try {
        const response = await fetch(`${ServiceProApp.API_BASE_URL}/client.php?action=mes_demandes&idClient=${currentUser.clientInfo.idClient}`);
        const result = await response.json();
        
        if (result.success) {
            currentDemandes = result.data;
            displayDemandes(currentDemandes);
        } else {
            showMessage('Erreur lors du chargement des demandes', 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur de connexion au serveur', 'error');
    }
}

// Affichage des demandes
function displayDemandes(demandes) {
    const container = document.getElementById('demandes-list');
    
    if (demandes.length === 0) {
        container.innerHTML = '<p class="no-data">Aucune demande trouvée.</p>';
        return;
    }
    
    container.innerHTML = demandes.map(demande => `
        <div class="demande-card" data-etat="${demande.etat}">
            <div class="card-header">
                <h3 class="card-title">${demande.categorie}</h3>
                <span class="status-badge status-${demande.etat}">${getEtatLabel(demande.etat)}</span>
            </div>
            <p class="demande-description">${demande.description}</p>
            <div class="demande-details">
                <p><strong>Date de demande:</strong> ${ServiceProApp.formatDate(demande.dateDemande)}</p>
                ${demande.budget ? `<p><strong>Budget:</strong> ${ServiceProApp.formatCurrency(demande.budget)}</p>` : ''}
                ${demande.prestataire_nom ? `<p><strong>Prestataire:</strong> ${demande.prestataire_nom} ${demande.prestataire_prenom}</p>` : ''}
                ${demande.adresseTravail ? `<p><strong>Adresse:</strong> ${demande.adresseTravail}</p>` : ''}
            </div>
            <div class="demande-actions">
                ${demande.etat === 'termine' ? `
                    <button onclick="showEvaluationModal(${demande.idDemandeClient}, ${demande.idPrestataire})" class="btn btn-primary">
                        <i class="fas fa-star"></i> Évaluer
                    </button>
                ` : ''}
                ${demande.etat === 'en_cours' ? `
                    <button onclick="confirmerFinTache(${demande.idDemandeClient})" class="btn btn-success">
                        <i class="fas fa-check"></i> Confirmer fin
                    </button>
                ` : ''}
                <button onclick="consulterDevis(${demande.idDemandeClient})" class="btn btn-primary">
                    <i class="fas fa-file-invoice"></i> Voir devis
                </button>
            </div>
        </div>
    `).join('');
}

// Filtrage des demandes
function filterDemandes(etat) {
    // Mettre à jour les boutons de filtre
    document.querySelectorAll('.filter-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');
    
    // Filtrer les demandes
    let filteredDemandes = currentDemandes;
    if (etat !== 'toutes') {
        filteredDemandes = currentDemandes.filter(demande => demande.etat === etat);
    }
    
    displayDemandes(filteredDemandes);
}

// Chargement des devis
async function loadDevis() {
    try {
        const devisPromises = currentDemandes.map(demande => 
            fetch(`${ServiceProApp.API_BASE_URL}/client.php?action=consulter_devis&idDemandeClient=${demande.idDemandeClient}`)
                .then(response => response.json())
                .then(result => result.success ? result.data : [])
        );
        
        const allDevis = await Promise.all(devisPromises);
        currentDevis = allDevis.flat();
        
        displayDevis(currentDevis);
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur lors du chargement des devis', 'error');
    }
}

// Affichage des devis
function displayDevis(devis) {
    const container = document.getElementById('devis-list');
    
    if (devis.length === 0) {
        container.innerHTML = '<p class="no-data">Aucun devis trouvé.</p>';
        return;
    }
    
    container.innerHTML = devis.map(devisItem => `
        <div class="devis-card">
            <div class="card-header">
                <h3 class="card-title">Devis #${devisItem.idDevis}</h3>
                <span class="status-badge status-${devisItem.etat}">${getEtatLabel(devisItem.etat)}</span>
            </div>
            <p><strong>Prestataire:</strong> ${devisItem.prestataire_nom} ${devisItem.prestataire_prenom}</p>
            <p><strong>Montant:</strong> ${ServiceProApp.formatCurrency(devisItem.montantGlobal)}</p>
            <p><strong>Date limite:</strong> ${ServiceProApp.formatDate(devisItem.dateLimite)}</p>
            ${devisItem.description ? `<p><strong>Description:</strong> ${devisItem.description}</p>` : ''}
            ${devisItem.etat === 'en_attente' ? `
                <div class="devis-actions">
                    <button onclick="accepterDevis(${devisItem.idDevis})" class="btn btn-success">
                        <i class="fas fa-check"></i> Accepter
                    </button>
                    <button onclick="refuserDevis(${devisItem.idDevis})" class="btn btn-danger">
                        <i class="fas fa-times"></i> Refuser
                    </button>
                </div>
            ` : ''}
        </div>
    `).join('');
}

// Accepter un devis
async function accepterDevis(idDevis) {
    const avisClient = prompt('Votre avis sur ce devis (optionnel):');
    
    try {
        const response = await fetch(`${ServiceProApp.API_BASE_URL}/client.php?action=accepter_devis`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                idDevis: idDevis,
                avisClient: avisClient || ''
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('Devis accepté avec succès !', 'success');
            await loadDevis();
            await loadMesDemandes();
            updateDashboardStats();
        } else {
            showMessage(result.message || 'Erreur lors de l\'acceptation', 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur de connexion au serveur', 'error');
    }
}

// Confirmer la fin d'une tâche
async function confirmerFinTache(idDemandeClient) {
    if (!confirm('Êtes-vous sûr que la tâche est terminée ?')) {
        return;
    }
    
    try {
        const response = await fetch(`${ServiceProApp.API_BASE_URL}/client.php?action=confirmer_fin_tache`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                idDemandeClient: idDemandeClient
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('Tâche confirmée comme terminée !', 'success');
            await loadMesDemandes();
            updateDashboardStats();
        } else {
            showMessage(result.message || 'Erreur lors de la confirmation', 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur de connexion au serveur', 'error');
    }
}

// Chargement du profil
async function loadProfile() {
    try {
        const response = await fetch(`${ServiceProApp.API_BASE_URL}/client.php?action=profile&idCompte=${currentUser.idCompte}`);
        const result = await response.json();
        
        if (result.success) {
            const profile = result.data;
            
            // Remplir le formulaire de profil
            document.getElementById('profile-nom').value = profile.nom || '';
            document.getElementById('profile-prenom').value = profile.prenom || '';
            document.getElementById('profile-email').value = currentUser.email || '';
            document.getElementById('profile-telephone').value = profile.numeroTelephone || '';
            document.getElementById('profile-adresse').value = profile.adresse || '';
            document.getElementById('profile-naissance').value = profile.dateNaissance || '';
            document.getElementById('profile-sexe').value = profile.sexe || 'Homme';
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur lors du chargement du profil', 'error');
    }
}

// Mise à jour du profil
async function handleProfileUpdate(event) {
    event.preventDefault();
    
    const formData = {
        idClient: currentUser.clientInfo.idClient,
        nom: document.getElementById('profile-nom').value,
        prenom: document.getElementById('profile-prenom').value,
        numeroTelephone: document.getElementById('profile-telephone').value,
        adresse: document.getElementById('profile-adresse').value,
        dateNaissance: document.getElementById('profile-naissance').value,
        sexe: document.getElementById('profile-sexe').value
    };
    
    try {
        const response = await fetch(`${ServiceProApp.API_BASE_URL}/client.php?action=profile`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('Profil mis à jour avec succès !', 'success');
        } else {
            showMessage(result.message || 'Erreur lors de la mise à jour', 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur de connexion au serveur', 'error');
    }
}

// Mise à jour des statistiques du tableau de bord
function updateDashboardStats() {
    const totalDemandes = currentDemandes.length;
    const demandesEnCours = currentDemandes.filter(d => d.etat === 'en_cours' || d.etat === 'accepte').length;
    const demandesTerminees = currentDemandes.filter(d => d.etat === 'termine').length;
    const montantTotal = currentDemandes.reduce((total, d) => total + (parseFloat(d.budget) || 0), 0);
    
    document.getElementById('total-demandes').textContent = totalDemandes;
    document.getElementById('demandes-en-cours').textContent = demandesEnCours;
    document.getElementById('demandes-terminees').textContent = demandesTerminees;
    document.getElementById('montant-total').textContent = ServiceProApp.formatCurrency(montantTotal);
}

// Chargement de l'activité récente
function loadRecentActivity() {
    const recentDemandes = currentDemandes
        .sort((a, b) => new Date(b.dateDemande) - new Date(a.dateDemande))
        .slice(0, 5);
    
    const container = document.getElementById('recent-activities');
    
    if (recentDemandes.length === 0) {
        container.innerHTML = '<p class="no-data">Aucune activité récente.</p>';
        return;
    }
    
    container.innerHTML = recentDemandes.map(demande => `
        <div class="activity-item">
            <h4>${demande.categorie}</h4>
            <p>État: ${getEtatLabel(demande.etat)} - ${ServiceProApp.formatDate(demande.dateDemande)}</p>
        </div>
    `).join('');
}

// Utilitaires
function getEtatLabel(etat) {
    const labels = {
        'en_attente': 'En attente',
        'devis_propose': 'Devis proposé',
        'accepte': 'Accepté',
        'en_cours': 'En cours',
        'termine': 'Terminé',
        'annule': 'Annulé'
    };
    return labels[etat] || etat;
}

// Gestion de la sidebar mobile
function toggleSidebar() {
    document.querySelector('.sidebar').classList.toggle('open');
}

// Fermer la sidebar en cliquant à l'extérieur sur mobile
document.addEventListener('click', function(event) {
    const sidebar = document.querySelector('.sidebar');
    const toggle = document.querySelector('.sidebar-toggle');
    
    if (window.innerWidth <= 768 && 
        !sidebar.contains(event.target) && 
        !toggle.contains(event.target) && 
        sidebar.classList.contains('open')) {
        sidebar.classList.remove('open');
    }
});

// Déconnexion
function logout() {
    localStorage.removeItem('currentUser');
    window.location.href = '../../index.html';
}

// Configuration des étoiles de notation
function setupRatingStars() {
    const stars = document.querySelectorAll('.rating-stars i');
    stars.forEach((star, index) => {
        star.addEventListener('click', function() {
            const rating = index + 1;
            document.getElementById('eval-note').value = rating;
            
            // Mettre à jour l'affichage des étoiles
            stars.forEach((s, i) => {
                if (i < rating) {
                    s.classList.add('active');
                } else {
                    s.classList.remove('active');
                }
            });
        });
        
        star.addEventListener('mouseover', function() {
            const rating = index + 1;
            stars.forEach((s, i) => {
                if (i < rating) {
                    s.style.color = '#f39c12';
                } else {
                    s.style.color = '#ddd';
                }
            });
        });
    });
    
    document.querySelector('.rating-stars').addEventListener('mouseleave', function() {
        const currentRating = parseInt(document.getElementById('eval-note').value) || 0;
        stars.forEach((s, i) => {
            if (i < currentRating) {
                s.style.color = '#f39c12';
                s.classList.add('active');
            } else {
                s.style.color = '#ddd';
                s.classList.remove('active');
            }
        });
    });
}

// Afficher le modal d'évaluation
function showEvaluationModal(idDemandeClient, idPrestataire) {
    document.getElementById('eval-demande-id').value = idDemandeClient;
    document.getElementById('eval-prestataire-id').value = idPrestataire;
    document.getElementById('evaluationModal').style.display = 'block';
}

// Soumettre une évaluation
async function handleEvaluationSubmit(event) {
    event.preventDefault();
    
    const formData = {
        idClient: currentUser.clientInfo.idClient,
        idPrestataire: document.getElementById('eval-prestataire-id').value,
        idDemandeClient: document.getElementById('eval-demande-id').value,
        note: document.getElementById('eval-note').value,
        commentaire: document.getElementById('eval-commentaire').value
    };
    
    if (!formData.note) {
        showMessage('Veuillez sélectionner une note', 'error');
        return;
    }
    
    try {
        const response = await fetch(`${ServiceProApp.API_BASE_URL}/client.php?action=evaluer_prestataire`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('Évaluation soumise avec succès !', 'success');
            closeModal('evaluationModal');
            document.getElementById('evaluation-form').reset();
            
            // Réinitialiser les étoiles
            document.querySelectorAll('.rating-stars i').forEach(star => {
                star.classList.remove('active');
                star.style.color = '#ddd';
            });
        } else {
            showMessage(result.message || 'Erreur lors de la soumission', 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur de connexion au serveur', 'error');
    }
}

// Afficher le modal de plainte
function showComplaintModal() {
    // Charger la liste des prestataires avec qui le client a travaillé
    loadPrestatairesPourPlainte();
    document.getElementById('complaintModal').style.display = 'block';
}

// Charger les prestataires pour la plainte
function loadPrestatairesPourPlainte() {
    const prestataires = currentDemandes
        .filter(d => d.idPrestataire && d.prestataire_nom)
        .map(d => ({
            id: d.idPrestataire,
            nom: `${d.prestataire_nom} ${d.prestataire_prenom}`
        }))
        .filter((p, index, self) => self.findIndex(item => item.id === p.id) === index);
    
    const select = document.getElementById('complaint-prestataire');
    select.innerHTML = '<option value="">Sélectionner un prestataire</option>' +
        prestataires.map(p => `<option value="${p.id}">${p.nom}</option>`).join('');
}

// Soumettre une plainte
async function handleComplaintSubmit(event) {
    event.preventDefault();
    
    const formData = {
        idClient: currentUser.clientInfo.idClient,
        idPrestataire: document.getElementById('complaint-prestataire').value,
        sujet: document.getElementById('complaint-sujet').value,
        description: document.getElementById('complaint-description').value,
        contenu: document.getElementById('complaint-contenu').value
    };
    
    try {
        const response = await fetch(`${ServiceProApp.API_BASE_URL}/client.php?action=poser_plainte`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showMessage('Plainte soumise avec succès !', 'success');
            closeModal('complaintModal');
            document.getElementById('complaint-form').reset();
            
            // Recharger les plaintes si on est sur cette section
            if (document.getElementById('plaintes').classList.contains('active')) {
                loadPlaintes();
            }
        } else {
            showMessage(result.message || 'Erreur lors de la soumission', 'error');
        }
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur de connexion au serveur', 'error');
    }
}

// Chargement des factures
async function loadFactures() {
    try {
        const facturesPromises = currentDemandes.map(demande => 
            fetch(`${ServiceProApp.API_BASE_URL}/client.php?action=consulter_facture&idDemandeClient=${demande.idDemandeClient}`)
                .then(response => response.json())
                .then(result => result.success ? result.data : null)
                .catch(() => null)
        );
        
        const allFactures = await Promise.all(facturesPromises);
        const factures = allFactures.filter(f => f !== null);
        
        displayFactures(factures);
    } catch (error) {
        console.error('Erreur:', error);
        showMessage('Erreur lors du chargement des factures', 'error');
    }
}

// Affichage des factures
function displayFactures(factures) {
    const container = document.getElementById('factures-list');
    
    if (factures.length === 0) {
        container.innerHTML = '<p class="no-data">Aucune facture trouvée.</p>';
        return;
    }
    
    container.innerHTML = factures.map(facture => `
        <div class="facture-card">
            <div class="card-header">
                <h3 class="card-title">Facture #${facture.idFacture}</h3>
                <span class="status-badge status-${facture.etat}">${getEtatLabel(facture.etat)}</span>
            </div>
            <p><strong>Montant:</strong> ${ServiceProApp.formatCurrency(facture.montantTotal)}</p>
            <p><strong>Date d'émission:</strong> ${ServiceProApp.formatDate(facture.dateEmission)}</p>
            <p><strong>Date d'échéance:</strong> ${ServiceProApp.formatDate(facture.dateEcheance)}</p>
            <p><strong>Mode de paiement:</strong> ${facture.modePaiement}</p>
            ${facture.description ? `<p><strong>Description:</strong> ${facture.description}</p>` : ''}
        </div>
    `).join('');
}

// Chargement des évaluations
async function loadEvaluations() {
    // Pour l'instant, afficher un message car cette fonctionnalité nécessite un endpoint spécifique
    const container = document.getElementById('evaluations-list');
    container.innerHTML = '<p class="no-data">Fonctionnalité en cours de développement.</p>';
}

// Chargement des plaintes
async function loadPlaintes() {
    // Pour l'instant, afficher un message car cette fonctionnalité nécessite un endpoint spécifique
    const container = document.getElementById('plaintes-list');
    container.innerHTML = '<p class="no-data">Fonctionnalité en cours de développement.</p>';
}

// Fermer les modals
function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// Fermer les modals en cliquant à l'extérieur
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });
}

// Consulter les devis d'une demande
function consulterDevis(idDemandeClient) {
    showSection('devis');
    // Optionnel: filtrer pour afficher seulement les devis de cette demande
}

// Fonction utilitaire pour afficher les messages
function showMessage(message, type = 'info') {
    if (window.ServiceProApp && window.ServiceProApp.showMessage) {
        window.ServiceProApp.showMessage(message, type);
    } else {
        alert(message);
    }
}
