<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tableau de bord Prestataire - ServicePro</title>
    <link rel="stylesheet" href="../client_dashboard/style.css">
    <link rel="stylesheet" href="style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div id="app">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo">
                    <i class="fas fa-tools"></i>
                    <h2>ServicePro</h2>
                </div>
            </div>
            <nav class="sidebar-nav">
                <a href="#" onclick="showSection('dashboard')" class="nav-item active">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Tableau de bord</span>
                </a>
                <a href="#" onclick="showSection('demandes-disponibles')" class="nav-item">
                    <i class="fas fa-search"></i>
                    <span>Demandes disponibles</span>
                </a>
                <a href="#" onclick="showSection('mes-prises-en-charge')" class="nav-item">
                    <i class="fas fa-briefcase"></i>
                    <span>Mes prises en charge</span>
                </a>
                <a href="#" onclick="showSection('proposer-devis')" class="nav-item">
                    <i class="fas fa-file-invoice-dollar"></i>
                    <span>Proposer devis</span>
                </a>
                <a href="#" onclick="showSection('mes-evaluations')" class="nav-item">
                    <i class="fas fa-star"></i>
                    <span>Mes évaluations</span>
                </a>
                <a href="#" onclick="showSection('plaintes')" class="nav-item">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>Plaintes</span>
                </a>
                <a href="#" onclick="showSection('profil')" class="nav-item">
                    <i class="fas fa-user"></i>
                    <span>Mon profil</span>
                </a>
            </nav>
            <div class="sidebar-footer">
                <a href="../../index.html" class="nav-item">
                    <i class="fas fa-home"></i>
                    <span>Accueil</span>
                </a>
                <a href="#" onclick="logout()" class="nav-item">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Déconnexion</span>
                </a>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button class="sidebar-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 id="page-title">Tableau de bord</h1>
                </div>
                <div class="header-right">
                    <div class="availability-toggle">
                        <label class="switch">
                            <input type="checkbox" id="availability-switch">
                            <span class="slider"></span>
                        </label>
                        <span id="availability-text">Disponible</span>
                    </div>
                    <div class="user-info">
                        <span id="user-name">Chargement...</span>
                        <i class="fas fa-user-circle"></i>
                    </div>
                </div>
            </header>

            <!-- Dashboard Section -->
            <section id="dashboard" class="content-section active">
                <div class="dashboard-stats">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="total-prises-en-charge">0</h3>
                            <p>Prises en charge</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="taches-en-cours">0</h3>
                            <p>En cours</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="taches-terminees">0</h3>
                            <p>Terminées</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-star"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="note-moyenne">0.0</h3>
                            <p>Note moyenne</p>
                        </div>
                    </div>
                </div>

                <div class="dashboard-content">
                    <div class="recent-activity">
                        <h3>Activité récente</h3>
                        <div id="recent-activities" class="activity-list">
                            <!-- Les activités seront chargées ici -->
                        </div>
                    </div>
                    <div class="quick-actions">
                        <h3>Actions rapides</h3>
                        <div class="action-buttons">
                            <button onclick="showSection('demandes-disponibles')" class="action-btn">
                                <i class="fas fa-search"></i>
                                Voir demandes
                            </button>
                            <button onclick="showSection('mes-prises-en-charge')" class="action-btn">
                                <i class="fas fa-briefcase"></i>
                                Mes tâches
                            </button>
                            <button onclick="showSection('proposer-devis')" class="action-btn">
                                <i class="fas fa-file-invoice-dollar"></i>
                                Nouveau devis
                            </button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Demandes Disponibles Section -->
            <section id="demandes-disponibles" class="content-section">
                <div class="section-header">
                    <h2>Demandes disponibles</h2>
                    <div class="filter-buttons">
                        <button onclick="filterDemandesDisponibles('toutes')" class="filter-btn active">Toutes</button>
                        <button onclick="filterDemandesDisponibles('mon-service')" class="filter-btn">Mon service</button>
                    </div>
                </div>
                
                <div id="demandes-disponibles-list" class="demandes-grid">
                    <!-- Les demandes seront chargées ici -->
                </div>
            </section>

            <!-- Mes Prises en Charge Section -->
            <section id="mes-prises-en-charge" class="content-section">
                <div class="section-header">
                    <h2>Mes prises en charge</h2>
                    <div class="filter-buttons">
                        <button onclick="filterPrisesEnCharge('toutes')" class="filter-btn active">Toutes</button>
                        <button onclick="filterPrisesEnCharge('en_cours')" class="filter-btn">En cours</button>
                        <button onclick="filterPrisesEnCharge('termine')" class="filter-btn">Terminées</button>
                    </div>
                </div>
                
                <div id="prises-en-charge-list" class="demandes-grid">
                    <!-- Les prises en charge seront chargées ici -->
                </div>
            </section>

            <!-- Proposer Devis Section -->
            <section id="proposer-devis" class="content-section">
                <div class="section-header">
                    <h2>Proposer un devis</h2>
                    <p>Sélectionnez une demande et proposez votre devis</p>
                </div>
                
                <div class="devis-form-container">
                    <div class="demande-selection">
                        <h3>Sélectionner une demande</h3>
                        <div id="demandes-pour-devis" class="demandes-selection-list">
                            <!-- Les demandes seront chargées ici -->
                        </div>
                    </div>
                    
                    <form id="devis-form" class="devis-form" style="display: none;">
                        <h3>Détails du devis</h3>
                        <input type="hidden" id="devis-demande-id">
                        
                        <div class="form-group">
                            <label for="devis-description">Description des travaux</label>
                            <textarea id="devis-description" rows="4" 
                                     placeholder="Décrivez en détail les travaux que vous proposez"></textarea>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="devis-montant">Montant global (€) *</label>
                                <input type="number" id="devis-montant" min="0" step="0.01" required>
                            </div>
                            <div class="form-group">
                                <label for="devis-date-limite">Date limite de validité *</label>
                                <input type="date" id="devis-date-limite" required>
                            </div>
                        </div>
                        
                        <div class="matiere-premiere-section">
                            <h4>Matières premières (optionnel)</h4>
                            <div id="matieres-list">
                                <!-- Les matières seront ajoutées ici -->
                            </div>
                            <button type="button" onclick="ajouterMatiere()" class="btn btn-outline">
                                <i class="fas fa-plus"></i> Ajouter matière
                            </button>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                            Envoyer le devis
                        </button>
                    </form>
                </div>
            </section>

            <!-- Mes Évaluations Section -->
            <section id="mes-evaluations" class="content-section">
                <div class="section-header">
                    <h2>Mes évaluations</h2>
                    <p>Consultez les évaluations de vos clients</p>
                </div>
                
                <div id="evaluations-list" class="evaluations-grid">
                    <!-- Les évaluations seront chargées ici -->
                </div>
            </section>

            <!-- Plaintes Section -->
            <section id="plaintes" class="content-section">
                <div class="section-header">
                    <h2>Mes plaintes</h2>
                    <button onclick="showComplaintModal()" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Nouvelle plainte
                    </button>
                </div>
                
                <div id="plaintes-list" class="plaintes-grid">
                    <!-- Les plaintes seront chargées ici -->
                </div>
            </section>

            <!-- Profil Section -->
            <section id="profil" class="content-section">
                <div class="section-header">
                    <h2>Mon profil</h2>
                    <p>Gérez vos informations professionnelles</p>
                </div>
                
                <form id="profile-form" class="profile-form">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="profile-nom">Nom *</label>
                            <input type="text" id="profile-nom" required>
                        </div>
                        <div class="form-group">
                            <label for="profile-prenom">Prénom *</label>
                            <input type="text" id="profile-prenom" required>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="profile-email">Email</label>
                        <input type="email" id="profile-email" readonly>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="profile-telephone">Téléphone</label>
                            <input type="tel" id="profile-telephone">
                        </div>
                        <div class="form-group">
                            <label for="profile-service">Type de service</label>
                            <input type="text" id="profile-service" readonly>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="profile-adresse">Adresse</label>
                        <input type="text" id="profile-adresse">
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="profile-naissance">Date de naissance</label>
                            <input type="date" id="profile-naissance">
                        </div>
                        <div class="form-group">
                            <label for="profile-sexe">Sexe</label>
                            <select id="profile-sexe">
                                <option value="Homme">Homme</option>
                                <option value="Femme">Femme</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="profile-stats">
                        <div class="stat-item">
                            <span class="stat-label">Note moyenne:</span>
                            <span id="profile-note" class="stat-value">0.0/5</span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Nombre d'évaluations:</span>
                            <span id="profile-evaluations" class="stat-value">0</span>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        Sauvegarder
                    </button>
                </form>
            </section>
        </main>
    </div>

    <!-- Modal pour nouvelle plainte -->
    <div id="complaintModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('complaintModal')">&times;</span>
            <h2>Nouvelle plainte</h2>
            <form id="complaint-form">
                <div class="form-group">
                    <label for="complaint-client">Client concerné</label>
                    <select id="complaint-client" required>
                        <option value="">Sélectionner un client</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="complaint-sujet">Sujet *</label>
                    <input type="text" id="complaint-sujet" required>
                </div>
                <div class="form-group">
                    <label for="complaint-description">Description *</label>
                    <textarea id="complaint-description" rows="4" required></textarea>
                </div>
                <div class="form-group">
                    <label for="complaint-contenu">Détails supplémentaires</label>
                    <textarea id="complaint-contenu" rows="3"></textarea>
                </div>
                <button type="submit" class="btn btn-primary">Soumettre la plainte</button>
            </form>
        </div>
    </div>

    <!-- Modal pour mise à jour d'avancement -->
    <div id="avancementModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal('avancementModal')">&times;</span>
            <h2>Mettre à jour l'avancement</h2>
            <form id="avancement-form">
                <input type="hidden" id="avancement-demande-id">
                
                <div class="form-group">
                    <label for="avancement-etat">Nouvel état *</label>
                    <select id="avancement-etat" required>
                        <option value="">Sélectionner un état</option>
                        <option value="en_cours">En cours</option>
                        <option value="termine">Terminé</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="avancement-commentaire">Commentaire (optionnel)</label>
                    <textarea id="avancement-commentaire" rows="3" 
                             placeholder="Ajoutez un commentaire sur l'avancement"></textarea>
                </div>
                
                <button type="submit" class="btn btn-primary">Mettre à jour</button>
            </form>
        </div>
    </div>

    <script src="../../script.js"></script>
    <script src="script.js"></script>
</body>
</html>
