<?php
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST, GET, PUT, DELETE, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Gestion des requêtes OPTIONS (preflight)
if ($_SERVER['REQUEST_METHOD'] == 'OPTIONS') {
    exit(0);
}

require_once 'db.php';

$database = new Database();
$db = $database->getConnection();

$auth = new Auth($db);
$demandeManager = new DemandeClientManager($db);

// Récupérer l'action depuis l'URL ou POST
$action = isset($_GET['action']) ? $_GET['action'] : (isset($_POST['action']) ? $_POST['action'] : '');

switch($action) {
    case 'login':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            if (!isset($input['email']) || !isset($input['password'])) {
                jsonResponse(array("success" => false, "message" => "Email et mot de passe requis"));
            }
            
            $result = $auth->login($input['email'], $input['password']);
            
            if ($result['success'] && ($result['typeCompte'] == 'admin' || $result['typeCompte'] == 'service_client')) {
                // Récupérer les informations selon le type
                if ($result['typeCompte'] == 'admin') {
                    $query = "SELECT * FROM Admin WHERE idCompte = :idCompte";
                } else {
                    $query = "SELECT * FROM ServiceClient WHERE idCompte = :idCompte";
                }
                
                $stmt = $db->prepare($query);
                $stmt->bindParam(":idCompte", $result['idCompte']);
                $stmt->execute();
                
                if ($stmt->rowCount() > 0) {
                    $userInfo = $stmt->fetch(PDO::FETCH_ASSOC);
                    $result['userInfo'] = $userInfo;
                }
                jsonResponse($result);
            } else {
                jsonResponse(array("success" => false, "message" => "Accès non autorisé pour ce type de compte"));
            }
        }
        break;

    case 'demandes_prestataires':
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            $etat = isset($_GET['etat']) ? $_GET['etat'] : '';
            
            $query = "SELECT * FROM DemandePrestataire";
            if (!empty($etat)) {
                $query .= " WHERE etat = :etat";
            }
            $query .= " ORDER BY dateDemande DESC";
            
            $stmt = $db->prepare($query);
            if (!empty($etat)) {
                $stmt->bindParam(":etat", $etat);
            }
            $stmt->execute();
            
            $demandes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            jsonResponse(array("success" => true, "data" => $demandes));
        }
        break;

    case 'gerer_inscription_prestataire':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            if (!isset($input['idDemandePrestataire']) || !isset($input['action'])) {
                jsonResponse(array("success" => false, "message" => "ID demande et action requis"));
            }
            
            $action_demande = $input['action']; // 'accepter' ou 'refuser'
            $nouvel_etat = ($action_demande == 'accepter') ? 'accepte' : 'refuse';
            
            // Mettre à jour l'état de la demande
            $query = "UPDATE DemandePrestataire SET etat = :etat WHERE idDemandePrestataire = :idDemandePrestataire";
            $stmt = $db->prepare($query);
            $stmt->bindParam(":etat", $nouvel_etat);
            $stmt->bindParam(":idDemandePrestataire", $input['idDemandePrestataire']);
            
            if ($stmt->execute()) {
                // Si accepté, créer le compte prestataire
                if ($action_demande == 'accepter') {
                    // Récupérer les données de la demande
                    $getQuery = "SELECT * FROM DemandePrestataire WHERE idDemandePrestataire = :idDemandePrestataire";
                    $getStmt = $db->prepare($getQuery);
                    $getStmt->bindParam(":idDemandePrestataire", $input['idDemandePrestataire']);
                    $getStmt->execute();
                    $demande = $getStmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($demande) {
                        // Créer le compte
                        $password_temp = 'temp123'; // Mot de passe temporaire
                        $accountResult = $auth->register($demande['email'], $password_temp, 'prestataire');
                        
                        if ($accountResult['success']) {
                            // Créer le profil prestataire
                            $prestataireQuery = "INSERT INTO Prestataire 
                                               (nom, prenom, numeroTelephone, dateNaissance, adresse, nin, typeService, 
                                                cv, casierJudiciaire, idCompte, idDemandePrestataire) 
                                               VALUES (:nom, :prenom, :numeroTelephone, :dateNaissance, :adresse, :nin, 
                                                       :typeService, :cv, :casierJudiciaire, :idCompte, :idDemandePrestataire)";
                            
                            $prestataireStmt = $db->prepare($prestataireQuery);
                            $prestataireStmt->bindParam(":nom", $demande['nom']);
                            $prestataireStmt->bindParam(":prenom", $demande['prenom']);
                            $prestataireStmt->bindParam(":numeroTelephone", $demande['numTel']);
                            $prestataireStmt->bindParam(":dateNaissance", $demande['dateNaissance']);
                            $prestataireStmt->bindParam(":adresse", $demande['adresse']);
                            $prestataireStmt->bindParam(":nin", $demande['nin']);
                            $prestataireStmt->bindParam(":typeService", $demande['typeService']);
                            $prestataireStmt->bindParam(":cv", $demande['cv']);
                            $prestataireStmt->bindParam(":casierJudiciaire", $demande['casierJudiciaire']);
                            $prestataireStmt->bindParam(":idCompte", $accountResult['idCompte']);
                            $prestataireStmt->bindParam(":idDemandePrestataire", $input['idDemandePrestataire']);
                            
                            $prestataireStmt->execute();
                        }
                    }
                }
                
                jsonResponse(array("success" => true, "message" => "Demande traitée avec succès"));
            } else {
                jsonResponse(array("success" => false, "message" => "Erreur lors du traitement de la demande"));
            }
        }
        break;

    case 'liste_prestataires':
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            $query = "SELECT p.*, c.email, 
                             (SELECT COUNT(*) FROM Evaluation WHERE idPrestataire = p.idPrestataire) as nb_evaluations
                      FROM Prestataire p 
                      JOIN Compte c ON p.idCompte = c.idCompte 
                      ORDER BY p.nom, p.prenom";
            
            $stmt = $db->prepare($query);
            $stmt->execute();
            
            $prestataires = $stmt->fetchAll(PDO::FETCH_ASSOC);
            jsonResponse(array("success" => true, "data" => $prestataires));
        }
        break;

    case 'affecter_prestataire':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            if (!isset($input['idDemandeClient']) || !isset($input['idPrestataire'])) {
                jsonResponse(array("success" => false, "message" => "ID demande et ID prestataire requis"));
            }
            
            $result = $demandeManager->assignPrestataire($input['idDemandeClient'], $input['idPrestataire']);
            jsonResponse($result);
        }
        break;

    case 'toutes_demandes':
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            $demandes = $demandeManager->getAll();
            jsonResponse(array("success" => true, "data" => $demandes));
        }
        break;

    case 'statistiques':
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            // Statistiques générales
            $stats = array();
            
            // Nombre total de clients
            $query = "SELECT COUNT(*) as total FROM Client";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $stats['total_clients'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // Nombre total de prestataires
            $query = "SELECT COUNT(*) as total FROM Prestataire";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $stats['total_prestataires'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // Nombre total de demandes
            $query = "SELECT COUNT(*) as total FROM DemandeClient";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $stats['total_demandes'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            // Demandes par état
            $query = "SELECT etat, COUNT(*) as nombre FROM DemandeClient GROUP BY etat";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $stats['demandes_par_etat'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Prestataires par service
            $query = "SELECT typeService, COUNT(*) as nombre FROM Prestataire GROUP BY typeService";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $stats['prestataires_par_service'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Évaluations moyennes
            $query = "SELECT AVG(note) as moyenne FROM Evaluation";
            $stmt = $db->prepare($query);
            $stmt->execute();
            $stats['note_moyenne_globale'] = round($stmt->fetch(PDO::FETCH_ASSOC)['moyenne'], 2);
            
            jsonResponse(array("success" => true, "data" => $stats));
        }
        break;

    case 'plaintes':
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            $type = isset($_GET['type']) ? $_GET['type'] : 'toutes';
            
            if ($type == 'clients' || $type == 'toutes') {
                $query = "SELECT 'client' as type_plainte, pc.*, 
                                 c.nom as client_nom, c.prenom as client_prenom,
                                 p.nom as prestataire_nom, p.prenom as prestataire_prenom
                          FROM PlainteClient pc 
                          JOIN Client c ON pc.idClient = c.idClient 
                          JOIN Prestataire p ON pc.idPrestataire = p.idPrestataire";
                
                if ($type == 'toutes') {
                    $query .= " UNION ALL ";
                }
            }
            
            if ($type == 'prestataires' || $type == 'toutes') {
                $query .= "SELECT 'prestataire' as type_plainte, pp.idPlaintePrestataire as idPlainteClient, 
                                  pp.sujet, pp.description, pp.date, pp.contenu, pp.etat,
                                  pp.idPrestataire, pp.idClient,
                                  c.nom as client_nom, c.prenom as client_prenom,
                                  p.nom as prestataire_nom, p.prenom as prestataire_prenom
                           FROM PlaintePrestataire pp 
                           JOIN Client c ON pp.idClient = c.idClient 
                           JOIN Prestataire p ON pp.idPrestataire = p.idPrestataire";
            }
            
            $query .= " ORDER BY date DESC";
            
            $stmt = $db->prepare($query);
            $stmt->execute();
            
            $plaintes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            jsonResponse(array("success" => true, "data" => $plaintes));
        }
        break;

    case 'black_liste':
        if ($_SERVER['REQUEST_METHOD'] == 'GET') {
            // Prestataires avec de mauvaises évaluations ou beaucoup de plaintes
            $query = "SELECT p.*, c.email,
                             (SELECT AVG(note) FROM Evaluation WHERE idPrestataire = p.idPrestataire) as note_moyenne,
                             (SELECT COUNT(*) FROM PlainteClient WHERE idPrestataire = p.idPrestataire) as nb_plaintes
                      FROM Prestataire p 
                      JOIN Compte c ON p.idCompte = c.idCompte 
                      WHERE (SELECT AVG(note) FROM Evaluation WHERE idPrestataire = p.idPrestataire) < 2.5
                         OR (SELECT COUNT(*) FROM PlainteClient WHERE idPrestataire = p.idPrestataire) > 3
                      ORDER BY note_moyenne ASC, nb_plaintes DESC";
            
            $stmt = $db->prepare($query);
            $stmt->execute();
            
            $blacklist = $stmt->fetchAll(PDO::FETCH_ASSOC);
            jsonResponse(array("success" => true, "data" => $blacklist));
        }
        break;

    case 'bloquer_prestataire':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            if (!isset($input['idPrestataire'])) {
                jsonResponse(array("success" => false, "message" => "ID prestataire requis"));
            }
            
            $query = "UPDATE Prestataire SET estDisponible = FALSE WHERE idPrestataire = :idPrestataire";
            $stmt = $db->prepare($query);
            $stmt->bindParam(":idPrestataire", $input['idPrestataire']);
            
            if ($stmt->execute()) {
                jsonResponse(array("success" => true, "message" => "Prestataire bloqué avec succès"));
            } else {
                jsonResponse(array("success" => false, "message" => "Erreur lors du blocage"));
            }
        }
        break;

    case 'debloquer_prestataire':
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            $input = validateJsonInput();
            
            if (!isset($input['idPrestataire'])) {
                jsonResponse(array("success" => false, "message" => "ID prestataire requis"));
            }
            
            $query = "UPDATE Prestataire SET estDisponible = TRUE WHERE idPrestataire = :idPrestataire";
            $stmt = $db->prepare($query);
            $stmt->bindParam(":idPrestataire", $input['idPrestataire']);
            
            if ($stmt->execute()) {
                jsonResponse(array("success" => true, "message" => "Prestataire débloqué avec succès"));
            } else {
                jsonResponse(array("success" => false, "message" => "Erreur lors du déblocage"));
            }
        }
        break;

    default:
        jsonResponse(array("success" => false, "message" => "Action non reconnue"));
        break;
}
?>
